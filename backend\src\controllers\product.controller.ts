/**
 * 产品控制器
 */

import { Request, Response } from 'express';
import { DatabaseManager } from '../config/database';
import { logger } from '../utils/logger';
import { ApiResponse, PaginationHelper } from '../utils/response';
import { NotFoundError, ValidationError } from '../middleware/errorHandler';
import type { IProductQuery, ICreateProductDto, IUpdateProductDto } from '@/shared/types';

/**
 * 产品控制器
 */
export class ProductController {
  private prisma = DatabaseManager.getInstance().getPrisma();

  /**
   * 获取产品列表
   */
  public async getProducts(req: Request, res: Response): Promise<void> {
    const query = req.query as unknown as IProductQuery;
    
    // 验证和处理分页参数
    const { page, limit } = PaginationHelper.validateParams(
      Number(query.page) || 1,
      Number(query.limit) || 12
    );

    // 构建查询条件
    const where: any = {
      isActive: true,
    };

    // 分类筛选
    if (query.category) {
      where.category = {
        slug: query.category,
      };
    }

    // 搜索关键词
    if (query.search) {
      where.OR = [
        { name: { contains: query.search } },
        { description: { contains: query.search } },
        { shortDesc: { contains: query.search } },
        { brand: { contains: query.search } },
        { model: { contains: query.search } },
      ];
    }

    // 特色产品筛选
    if (query.featured !== undefined) {
      where.isFeatured = query.featured;
    }

    // 价格范围筛选
    if (query.minPrice !== undefined || query.maxPrice !== undefined) {
      where.price = {};
      if (query.minPrice !== undefined) {
        where.price.gte = query.minPrice;
      }
      if (query.maxPrice !== undefined) {
        where.price.lte = query.maxPrice;
      }
    }

    // 品牌筛选
    if (query.brand) {
      where.brand = query.brand;
    }

    // 排序
    const orderBy: any = {};
    if (query.sortBy) {
      const sortOrder = query.sortOrder || 'desc';
      switch (query.sortBy) {
        case 'price':
          orderBy.price = sortOrder;
          break;
        case 'name':
          orderBy.name = sortOrder;
          break;
        case 'created':
          orderBy.createdAt = sortOrder;
          break;
        case 'popular':
          orderBy.viewCount = sortOrder;
          break;
        default:
          orderBy.createdAt = 'desc';
      }
    } else {
      orderBy.createdAt = 'desc';
    }

    try {
      // 获取总数
      const total = await this.prisma.product.count({ where });

      // 获取产品列表
      const products = await this.prisma.product.findMany({
        where,
        include: {
          category: {
            select: {
              id: true,
              name: true,
              slug: true,
            },
          },
          images: {
            where: { isPrimary: true },
            take: 1,
            select: {
              url: true,
              alt: true,
            },
          },
          tags: {
            include: {
              tag: {
                select: {
                  id: true,
                  name: true,
                  slug: true,
                  color: true,
                },
              },
            },
          },
        },
        orderBy,
        skip: PaginationHelper.getSkip(page, limit),
        take: limit,
      });

      // 计算分页信息
      const pagination = PaginationHelper.calculate(page, limit, total);

      res.json(ApiResponse.paginated(products, pagination));
    } catch (error) {
      logger.error('获取产品列表失败', { error, query });
      throw error;
    }
  }

  /**
   * 获取产品详情
   */
  public async getProduct(req: Request, res: Response): Promise<void> {
    const { slug } = req.params;

    try {
      const product = await this.prisma.product.findUnique({
        where: { slug },
        include: {
          category: {
            select: {
              id: true,
              name: true,
              slug: true,
            },
          },
          images: {
            orderBy: { sortOrder: 'asc' },
            select: {
              id: true,
              url: true,
              alt: true,
              title: true,
              isPrimary: true,
            },
          },
          specifications: {
            orderBy: { sortOrder: 'asc' },
            select: {
              id: true,
              name: true,
              value: true,
              unit: true,
            },
          },
          reviews: {
            where: { isApproved: true },
            orderBy: { createdAt: 'desc' },
            take: 10,
            include: {
              user: {
                select: {
                  name: true,
                  avatar: true,
                },
              },
            },
          },
          tags: {
            include: {
              tag: {
                select: {
                  id: true,
                  name: true,
                  slug: true,
                  color: true,
                },
              },
            },
          },
        },
      });

      if (!product) {
        throw new NotFoundError('产品');
      }

      // 增加浏览次数
      await this.prisma.product.update({
        where: { id: product.id },
        data: { viewCount: { increment: 1 } },
      });

      // 获取相关产品
      const relatedProducts = await this.prisma.product.findMany({
        where: {
          categoryId: product.categoryId,
          id: { not: product.id },
          isActive: true,
        },
        include: {
          images: {
            where: { isPrimary: true },
            take: 1,
            select: {
              url: true,
              alt: true,
            },
          },
        },
        take: 4,
        orderBy: { viewCount: 'desc' },
      });

      res.json(ApiResponse.success({
        ...product,
        relatedProducts,
      }));
    } catch (error) {
      logger.error('获取产品详情失败', { error, slug });
      throw error;
    }
  }

  /**
   * 获取特色产品
   */
  public async getFeaturedProducts(req: Request, res: Response): Promise<void> {
    const limit = Number(req.query.limit) || 8;

    try {
      const products = await this.prisma.product.findMany({
        where: {
          isFeatured: true,
          isActive: true,
        },
        include: {
          category: {
            select: {
              id: true,
              name: true,
              slug: true,
            },
          },
          images: {
            where: { isPrimary: true },
            take: 1,
            select: {
              url: true,
              alt: true,
            },
          },
        },
        orderBy: { createdAt: 'desc' },
        take: limit,
      });

      res.json(ApiResponse.success(products));
    } catch (error) {
      logger.error('获取特色产品失败', { error });
      throw error;
    }
  }

  /**
   * 获取热门产品
   */
  public async getPopularProducts(req: Request, res: Response): Promise<void> {
    const limit = Number(req.query.limit) || 8;

    try {
      const products = await this.prisma.product.findMany({
        where: {
          isActive: true,
        },
        include: {
          category: {
            select: {
              id: true,
              name: true,
              slug: true,
            },
          },
          images: {
            where: { isPrimary: true },
            take: 1,
            select: {
              url: true,
              alt: true,
            },
          },
        },
        orderBy: { viewCount: 'desc' },
        take: limit,
      });

      res.json(ApiResponse.success(products));
    } catch (error) {
      logger.error('获取热门产品失败', { error });
      throw error;
    }
  }

  /**
   * 搜索产品
   */
  public async searchProducts(req: Request, res: Response): Promise<void> {
    const { q: query } = req.query;
    const { page = 1, limit = 12 } = req.query;

    if (!query || typeof query !== 'string') {
      throw new ValidationError('搜索关键词不能为空');
    }

    const { page: validPage, limit: validLimit } = PaginationHelper.validateParams(
      Number(page),
      Number(limit)
    );

    try {
      const where = {
        isActive: true,
        OR: [
          { name: { contains: query } },
          { description: { contains: query } },
          { shortDesc: { contains: query } },
          { brand: { contains: query } },
          { model: { contains: query } },
          { category: { name: { contains: query } } },
        ],
      };

      // 获取总数
      const total = await this.prisma.product.count({ where });

      // 获取搜索结果
      const products = await this.prisma.product.findMany({
        where,
        include: {
          category: {
            select: {
              id: true,
              name: true,
              slug: true,
            },
          },
          images: {
            where: { isPrimary: true },
            take: 1,
            select: {
              url: true,
              alt: true,
            },
          },
        },
        orderBy: [
          { isFeatured: 'desc' },
          { viewCount: 'desc' },
          { createdAt: 'desc' },
        ],
        skip: PaginationHelper.getSkip(validPage, validLimit),
        take: validLimit,
      });

      // 计算分页信息
      const pagination = PaginationHelper.calculate(validPage, validLimit, total);

      res.json(ApiResponse.paginated(products, pagination));
    } catch (error) {
      logger.error('搜索产品失败', { error, query });
      throw error;
    }
  }

  /**
   * 创建产品（管理员）
   */
  public async createProduct(req: Request, res: Response): Promise<void> {
    const data: ICreateProductDto = req.body;

    try {
      // 检查slug是否已存在
      const existingProduct = await this.prisma.product.findUnique({
        where: { slug: data.slug },
      });

      if (existingProduct) {
        throw new ValidationError('产品标识符已存在');
      }

      // 检查分类是否存在
      const category = await this.prisma.category.findUnique({
        where: { id: data.categoryId },
      });

      if (!category) {
        throw new NotFoundError('产品分类');
      }

      const product = await this.prisma.product.create({
        data: {
          ...data,
          stock: data.stock || 0,
          minStock: data.minStock || 0,
          maxStock: data.maxStock || 999999,
        },
        include: {
          category: {
            select: {
              id: true,
              name: true,
              slug: true,
            },
          },
        },
      });

      logger.info('产品创建成功', {
        productId: product.id,
        name: product.name,
        userId: req.user?.id,
      });

      res.status(201).json(ApiResponse.created(product));
    } catch (error) {
      logger.error('创建产品失败', { error, data });
      throw error;
    }
  }

  /**
   * 更新产品（管理员）
   */
  public async updateProduct(req: Request, res: Response): Promise<void> {
    const { id } = req.params;
    const data: IUpdateProductDto = req.body;

    try {
      const productId = parseInt(id);
      
      // 检查产品是否存在
      const existingProduct = await this.prisma.product.findUnique({
        where: { id: productId },
      });

      if (!existingProduct) {
        throw new NotFoundError('产品');
      }

      // 如果更新slug，检查是否已存在
      if (data.slug && data.slug !== existingProduct.slug) {
        const slugExists = await this.prisma.product.findUnique({
          where: { slug: data.slug },
        });

        if (slugExists) {
          throw new ValidationError('产品标识符已存在');
        }
      }

      // 如果更新分类，检查分类是否存在
      if (data.categoryId) {
        const category = await this.prisma.category.findUnique({
          where: { id: data.categoryId },
        });

        if (!category) {
          throw new NotFoundError('产品分类');
        }
      }

      const product = await this.prisma.product.update({
        where: { id: productId },
        data,
        include: {
          category: {
            select: {
              id: true,
              name: true,
              slug: true,
            },
          },
        },
      });

      logger.info('产品更新成功', {
        productId: product.id,
        name: product.name,
        userId: req.user?.id,
      });

      res.json(ApiResponse.updated(product));
    } catch (error) {
      logger.error('更新产品失败', { error, id, data });
      throw error;
    }
  }

  /**
   * 删除产品（管理员）
   */
  public async deleteProduct(req: Request, res: Response): Promise<void> {
    const { id } = req.params;

    try {
      const productId = parseInt(id);
      
      const product = await this.prisma.product.findUnique({
        where: { id: productId },
      });

      if (!product) {
        throw new NotFoundError('产品');
      }

      await this.prisma.product.delete({
        where: { id: productId },
      });

      logger.info('产品删除成功', {
        productId,
        name: product.name,
        userId: req.user?.id,
      });

      res.json(ApiResponse.deleted());
    } catch (error) {
      logger.error('删除产品失败', { error, id });
      throw error;
    }
  }
}
