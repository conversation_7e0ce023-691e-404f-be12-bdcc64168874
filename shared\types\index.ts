/**
 * 共享类型定义
 * 在前端、后端、管理后台之间共享的类型
 */

// 用户相关类型
export interface IUser {
  id: number;
  email: string;
  name: string;
  avatar?: string;
  role: UserRole;
  isActive: boolean;
  lastLoginAt?: Date;
  createdAt: Date;
  updatedAt: Date;
}

export enum UserRole {
  ADMIN = 'ADMIN',
  EDITOR = 'EDITOR',
  USER = 'USER'
}

// 产品相关类型
export interface IProduct {
  id: number;
  name: string;
  slug: string;
  description?: string;
  shortDesc?: string;
  sku?: string;
  price: number;
  originalPrice?: number;
  categoryId: number;
  brand?: string;
  model?: string;
  weight?: number;
  dimensions?: string;
  color?: string;
  material?: string;
  warranty?: string;
  stock: number;
  minStock: number;
  maxStock: number;
  isActive: boolean;
  isFeatured: boolean;
  isNew: boolean;
  viewCount: number;
  seoTitle?: string;
  seoDesc?: string;
  seoKeywords?: string;
  createdAt: Date;
  updatedAt: Date;
  category: ICategory;
  images: IProductImage[];
  specifications: IProductSpecification[];
  reviews: IProductReview[];
  tags: ITag[];
}

export interface ICreateProductDto {
  name: string;
  slug: string;
  description?: string;
  shortDesc?: string;
  sku?: string;
  price: number;
  originalPrice?: number;
  categoryId: number;
  brand?: string;
  model?: string;
  weight?: number;
  dimensions?: string;
  color?: string;
  material?: string;
  warranty?: string;
  stock?: number;
  minStock?: number;
  maxStock?: number;
  isActive?: boolean;
  isFeatured?: boolean;
  isNew?: boolean;
  seoTitle?: string;
  seoDesc?: string;
  seoKeywords?: string;
}

export interface IUpdateProductDto extends Partial<ICreateProductDto> {
  id: number;
}

// 分类相关类型
export interface ICategory {
  id: number;
  name: string;
  slug: string;
  description?: string;
  image?: string;
  parentId?: number;
  sortOrder: number;
  isActive: boolean;
  createdAt: Date;
  updatedAt: Date;
  parent?: ICategory;
  children: ICategory[];
  products: IProduct[];
}

export interface ICreateCategoryDto {
  name: string;
  slug: string;
  description?: string;
  image?: string;
  parentId?: number;
  sortOrder?: number;
  isActive?: boolean;
}

// 产品图片类型
export interface IProductImage {
  id: number;
  productId: number;
  url: string;
  alt?: string;
  title?: string;
  sortOrder: number;
  isPrimary: boolean;
  createdAt: Date;
}

// 产品规格类型
export interface IProductSpecification {
  id: number;
  productId: number;
  name: string;
  value: string;
  unit?: string;
  sortOrder: number;
}

// 产品评价类型
export interface IProductReview {
  id: number;
  productId: number;
  userId?: number;
  name: string;
  email: string;
  rating: number;
  title?: string;
  content: string;
  isApproved: boolean;
  createdAt: Date;
  updatedAt: Date;
  user?: IUser;
}

// 标签类型
export interface ITag {
  id: number;
  name: string;
  slug: string;
  color?: string;
  createdAt: Date;
}

// 公司信息类型
export interface ICompanyInfo {
  id: number;
  name: string;
  description?: string;
  logo?: string;
  address?: string;
  phone?: string;
  email?: string;
  website?: string;
  founded?: number;
  employees?: string;
  revenue?: string;
  mission?: string;
  vision?: string;
  values?: string;
  createdAt: Date;
  updatedAt: Date;
}

// 文章相关类型
export interface IArticle {
  id: number;
  title: string;
  slug: string;
  excerpt?: string;
  content: string;
  featuredImage?: string;
  authorId?: number;
  categoryId?: number;
  status: ArticleStatus;
  isSticky: boolean;
  viewCount: number;
  seoTitle?: string;
  seoDesc?: string;
  seoKeywords?: string;
  publishedAt?: Date;
  createdAt: Date;
  updatedAt: Date;
  author?: IUser;
  category?: IArticleCategory;
  tags: ITag[];
}

export enum ArticleStatus {
  DRAFT = 'DRAFT',
  PUBLISHED = 'PUBLISHED',
  ARCHIVED = 'ARCHIVED'
}

export interface IArticleCategory {
  id: number;
  name: string;
  slug: string;
  description?: string;
  sortOrder: number;
  createdAt: Date;
  updatedAt: Date;
}

// 联系咨询类型
export interface IContactInquiry {
  id: number;
  name: string;
  email: string;
  phone?: string;
  company?: string;
  subject: string;
  message: string;
  status: InquiryStatus;
  userId?: number;
  assignedTo?: number;
  repliedAt?: Date;
  createdAt: Date;
  updatedAt: Date;
  user?: IUser;
  assignee?: IUser;
}

export enum InquiryStatus {
  PENDING = 'PENDING',
  IN_PROGRESS = 'IN_PROGRESS',
  RESOLVED = 'RESOLVED',
  CLOSED = 'CLOSED'
}

// 系统设置类型
export interface ISetting {
  id: number;
  key: string;
  value?: string;
  type: string;
  group: string;
  label: string;
  description?: string;
  sortOrder: number;
  createdAt: Date;
  updatedAt: Date;
}

// 访问统计类型
export interface IAnalytics {
  id: number;
  date: Date;
  pagePath: string;
  pageTitle?: string;
  visitors: number;
  pageViews: number;
  bounceRate?: number;
  avgDuration?: number;
  referrer?: string;
  userAgent?: string;
  ipAddress?: string;
  country?: string;
  city?: string;
  device?: string;
  browser?: string;
  os?: string;
  createdAt: Date;
}

// API响应类型
export interface IApiResponse<T = any> {
  success: boolean;
  data: T;
  message?: string;
  errors?: string[];
}

export interface IPaginationResponse<T = any> {
  data: T[];
  pagination: {
    page: number;
    limit: number;
    total: number;
    totalPages: number;
    hasNext: boolean;
    hasPrev: boolean;
  };
}

// 查询参数类型
export interface IProductQuery {
  page: number;
  limit: number;
  category?: string;
  search?: string;
  sortBy?: string;
  sortOrder?: 'asc' | 'desc';
  featured?: boolean;
  minPrice?: number;
  maxPrice?: number;
  brand?: string;
}

export interface IArticleQuery {
  page: number;
  limit: number;
  category?: string;
  status?: ArticleStatus;
  search?: string;
  sortBy?: string;
  sortOrder?: 'asc' | 'desc';
}

// 认证相关类型
export interface ILoginDto {
  email: string;
  password: string;
}

export interface IRegisterDto {
  email: string;
  password: string;
  name: string;
}

export interface IAuthResponse {
  user: IUser;
  accessToken: string;
  refreshToken: string;
}

// 文件上传类型
export interface IUploadResponse {
  filename: string;
  originalName: string;
  mimetype: string;
  size: number;
  url: string;
}

// SEO相关类型
export interface ISEOData {
  title: string;
  description: string;
  keywords?: string;
  ogTitle?: string;
  ogDescription?: string;
  ogImage?: string;
  ogUrl?: string;
  twitterTitle?: string;
  twitterDescription?: string;
  twitterImage?: string;
}
