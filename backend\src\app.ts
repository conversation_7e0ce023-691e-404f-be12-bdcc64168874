/**
 * Express应用程序主文件
 */

import express from 'express';
import cors from 'cors';
import helmet from 'helmet';
import compression from 'compression';
import rateLimit from 'express-rate-limit';
import cookieParser from 'cookie-parser';
import path from 'path';
import { config } from './config';
import { logger, createRequestLogger } from './utils/logger';
import { errorHandler } from './middleware/errorHandler';
import { notFoundHandler } from './middleware/notFoundHandler';
import routes from './routes';

/**
 * 创建Express应用
 */
export function createApp(): express.Application {
  const app = express();

  // 信任代理
  app.set('trust proxy', 1);

  // 安全中间件
  app.use(helmet({
    contentSecurityPolicy: {
      directives: {
        defaultSrc: ["'self'"],
        styleSrc: ["'self'", "'unsafe-inline'"],
        scriptSrc: ["'self'"],
        imgSrc: ["'self'", "data:", "https:"],
        connectSrc: ["'self'"],
        fontSrc: ["'self'"],
        objectSrc: ["'none'"],
        mediaSrc: ["'self'"],
        frameSrc: ["'none'"],
      },
    },
    crossOriginEmbedderPolicy: false,
  }));

  // CORS配置
  app.use(cors({
    origin: config.cors.origin,
    credentials: config.cors.credentials,
    methods: ['GET', 'POST', 'PUT', 'DELETE', 'PATCH', 'OPTIONS'],
    allowedHeaders: ['Content-Type', 'Authorization', 'X-Requested-With'],
  }));

  // 压缩响应
  app.use(compression());

  // 请求体解析
  app.use(express.json({ limit: '10mb' }));
  app.use(express.urlencoded({ extended: true, limit: '10mb' }));

  // Cookie解析
  app.use(cookieParser());

  // 请求日志
  app.use(createRequestLogger());

  // 速率限制
  const limiter = rateLimit({
    windowMs: 15 * 60 * 1000, // 15分钟
    max: 1000, // 每个IP最多1000个请求
    message: {
      success: false,
      message: '请求过于频繁，请稍后再试',
    },
    standardHeaders: true,
    legacyHeaders: false,
  });
  app.use('/api', limiter);

  // 更严格的速率限制（认证相关）
  const authLimiter = rateLimit({
    windowMs: 15 * 60 * 1000, // 15分钟
    max: 10, // 每个IP最多10次认证请求
    message: {
      success: false,
      message: '认证请求过于频繁，请稍后再试',
    },
    standardHeaders: true,
    legacyHeaders: false,
  });
  app.use('/api/auth/login', authLimiter);
  app.use('/api/auth/register', authLimiter);
  app.use('/api/auth/reset-password', authLimiter);

  // 静态文件服务
  app.use('/uploads', express.static(path.join(config.upload.path), {
    maxAge: '1y',
    etag: true,
    lastModified: true,
  }));

  // API路由
  app.use('/api', routes);

  // 根路径
  app.get('/', (req, res) => {
    res.json({
      success: true,
      message: 'POS机产品展示网站 API 服务',
      version: process.env.npm_package_version || '1.0.0',
      timestamp: new Date().toISOString(),
      docs: '/api/docs',
    });
  });

  // 404处理
  app.use(notFoundHandler);

  // 全局错误处理
  app.use(errorHandler);

  return app;
}

/**
 * 启动服务器
 */
export async function startServer(): Promise<void> {
  try {
    const app = createApp();

    // 启动HTTP服务器
    const server = app.listen(config.port, config.host, () => {
      logger.info(`🚀 服务器启动成功`, {
        host: config.host,
        port: config.port,
        env: config.env,
        url: `http://${config.host}:${config.port}`,
      });
    });

    // 优雅关闭处理
    const gracefulShutdown = (signal: string) => {
      logger.info(`收到 ${signal} 信号，开始优雅关闭...`);
      
      server.close((err) => {
        if (err) {
          logger.error('服务器关闭失败:', err);
          process.exit(1);
        }
        
        logger.info('✓ 服务器已关闭');
        process.exit(0);
      });

      // 强制关闭超时
      setTimeout(() => {
        logger.error('强制关闭服务器');
        process.exit(1);
      }, 10000);
    };

    // 监听关闭信号
    process.on('SIGTERM', () => gracefulShutdown('SIGTERM'));
    process.on('SIGINT', () => gracefulShutdown('SIGINT'));

    // 未捕获的异常处理
    process.on('uncaughtException', (error) => {
      logger.error('未捕获的异常:', error);
      process.exit(1);
    });

    process.on('unhandledRejection', (reason, promise) => {
      logger.error('未处理的Promise拒绝:', { reason, promise });
      process.exit(1);
    });

  } catch (error) {
    logger.error('启动服务器失败:', error);
    process.exit(1);
  }
}
