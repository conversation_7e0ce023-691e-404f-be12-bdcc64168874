/**
 * API请求工具
 */

import type { IApiResponse, IPaginationResponse } from '@/shared/types'

interface ApiOptions {
  method?: 'GET' | 'POST' | 'PUT' | 'DELETE' | 'PATCH'
  body?: any
  headers?: Record<string, string>
  query?: Record<string, any>
  server?: boolean
  lazy?: boolean
  default?: () => any
}

/**
 * 基础API请求函数
 */
export const useApi = <T = any>(
  url: string,
  options: ApiOptions = {}
) => {
  const config = useRuntimeConfig()
  const { $toast } = useNuxtApp()

  // 构建完整URL
  const fullUrl = url.startsWith('http') ? url : `${config.public.apiBase}${url}`

  // 默认选项
  const defaultOptions: ApiOptions = {
    method: 'GET',
    server: true,
    lazy: false,
    ...options
  }

  // 请求头
  const headers: Record<string, string> = {
    'Content-Type': 'application/json',
    ...defaultOptions.headers
  }

  // 添加认证头
  const token = useCookie('accessToken')
  if (token.value) {
    headers.Authorization = `Bearer ${token.value}`
  }

  // 使用$fetch进行请求
  return $fetch<IApiResponse<T>>(fullUrl, {
    method: defaultOptions.method,
    headers,
    body: defaultOptions.body ? JSON.stringify(defaultOptions.body) : undefined,
    query: defaultOptions.query,
    onResponseError({ response }) {
      // 处理响应错误
      const errorMessage = response._data?.message || '请求失败'
      
      if (process.client && $toast) {
        $toast.error(errorMessage)
      }
      
      // 401错误处理
      if (response.status === 401) {
        // 清除认证信息
        const accessToken = useCookie('accessToken')
        const refreshToken = useCookie('refreshToken')
        accessToken.value = null
        refreshToken.value = null
        
        // 重定向到登录页
        if (process.client) {
          navigateTo('/login')
        }
      }
    }
  })
}

/**
 * GET请求
 */
export const useGet = <T = any>(url: string, query?: Record<string, any>) => {
  return useApi<T>(url, { method: 'GET', query })
}

/**
 * POST请求
 */
export const usePost = <T = any>(url: string, body?: any) => {
  return useApi<T>(url, { method: 'POST', body })
}

/**
 * PUT请求
 */
export const usePut = <T = any>(url: string, body?: any) => {
  return useApi<T>(url, { method: 'PUT', body })
}

/**
 * DELETE请求
 */
export const useDelete = <T = any>(url: string) => {
  return useApi<T>(url, { method: 'DELETE' })
}

/**
 * 分页请求
 */
export const usePaginatedApi = <T = any>(
  url: string,
  options: {
    page?: number
    limit?: number
    query?: Record<string, any>
  } = {}
) => {
  const { page = 1, limit = 10, query = {} } = options
  
  const queryParams = {
    page,
    limit,
    ...query
  }
  
  return useApi<IPaginationResponse<T>>(url, {
    method: 'GET',
    query: queryParams
  })
}

/**
 * 懒加载API请求
 */
export const useLazyApi = <T = any>(
  key: string,
  url: string,
  options: ApiOptions = {}
) => {
  const config = useRuntimeConfig()
  
  // 构建完整URL
  const fullUrl = url.startsWith('http') ? url : `${config.public.apiBase}${url}`
  
  return useLazyAsyncData<IApiResponse<T>>(key, async () => {
    return await useApi<T>(url, options)
  })
}

/**
 * 文件上传
 */
export const useUpload = (file: File, onProgress?: (progress: number) => void) => {
  const config = useRuntimeConfig()
  const { $toast } = useNuxtApp()
  
  return new Promise<IApiResponse<{ url: string; filename: string }>>((resolve, reject) => {
    const formData = new FormData()
    formData.append('file', file)
    
    const xhr = new XMLHttpRequest()
    
    // 上传进度
    if (onProgress) {
      xhr.upload.addEventListener('progress', (event) => {
        if (event.lengthComputable) {
          const progress = Math.round((event.loaded / event.total) * 100)
          onProgress(progress)
        }
      })
    }
    
    // 请求完成
    xhr.addEventListener('load', () => {
      if (xhr.status === 200) {
        try {
          const response = JSON.parse(xhr.responseText)
          resolve(response)
        } catch (error) {
          reject(new Error('响应解析失败'))
        }
      } else {
        reject(new Error(`上传失败: ${xhr.status}`))
      }
    })
    
    // 请求错误
    xhr.addEventListener('error', () => {
      reject(new Error('网络错误'))
    })
    
    // 添加认证头
    const token = useCookie('accessToken')
    if (token.value) {
      xhr.setRequestHeader('Authorization', `Bearer ${token.value}`)
    }
    
    // 发送请求
    xhr.open('POST', `${config.public.apiBase}/upload/image`)
    xhr.send(formData)
  })
}

/**
 * 产品相关API
 */
export const useProductsApi = () => {
  return {
    // 获取产品列表
    getProducts: (query?: Record<string, any>) => {
      return usePaginatedApi('/products', { query })
    },
    
    // 获取产品详情
    getProduct: (slug: string) => {
      return useGet(`/products/${slug}`)
    },
    
    // 获取特色产品
    getFeaturedProducts: () => {
      return useGet('/products/featured')
    },
    
    // 获取热门产品
    getPopularProducts: () => {
      return useGet('/products/popular')
    },
    
    // 搜索产品
    searchProducts: (query: string, filters?: Record<string, any>) => {
      return usePaginatedApi('/products/search', {
        query: { q: query, ...filters }
      })
    }
  }
}

/**
 * 分类相关API
 */
export const useCategoriesApi = () => {
  return {
    // 获取分类列表
    getCategories: () => {
      return useGet('/categories')
    },
    
    // 获取分类树
    getCategoryTree: () => {
      return useGet('/categories/tree')
    },
    
    // 获取分类详情
    getCategory: (slug: string) => {
      return useGet(`/categories/${slug}`)
    }
  }
}

/**
 * 文章相关API
 */
export const useArticlesApi = () => {
  return {
    // 获取文章列表
    getArticles: (query?: Record<string, any>) => {
      return usePaginatedApi('/articles', { query })
    },
    
    // 获取文章详情
    getArticle: (slug: string) => {
      return useGet(`/articles/${slug}`)
    }
  }
}

/**
 * 公司信息API
 */
export const useCompanyApi = () => {
  return {
    // 获取公司信息
    getCompanyInfo: () => {
      return useGet('/company/info')
    }
  }
}

/**
 * 联系咨询API
 */
export const useContactApi = () => {
  return {
    // 提交咨询
    submitInquiry: (data: {
      name: string
      email: string
      phone?: string
      company?: string
      subject: string
      message: string
    }) => {
      return usePost('/contact/submit', data)
    }
  }
}

/**
 * 认证相关API
 */
export const useAuthApi = () => {
  return {
    // 登录
    login: (email: string, password: string) => {
      return usePost('/auth/login', { email, password })
    },
    
    // 注册
    register: (data: { email: string; password: string; name: string }) => {
      return usePost('/auth/register', data)
    },
    
    // 登出
    logout: () => {
      return usePost('/auth/logout')
    },
    
    // 获取用户信息
    getProfile: () => {
      return useGet('/auth/profile')
    },
    
    // 刷新令牌
    refreshToken: (refreshToken: string) => {
      return usePost('/auth/refresh', { refreshToken })
    }
  }
}
