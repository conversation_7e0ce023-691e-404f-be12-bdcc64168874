---
description: 
globs: 
alwaysApply: true
---
# 部署指南与环境配置

## 宝塔LNMP环境配置

### 服务器环境要求
- **操作系统**: Rocky Linux 9.5
- **内存**: 最低2GB，推荐4GB+
- **存储**: 最低20GB，推荐50GB+
- **CPU**: 最低2核，推荐4核+
- **网络**: 公网IP，支持80/443端口

### 宝塔面板安装
```bash
# Rocky Linux 9.5 安装宝塔面板
yum install -y wget && wget -O install.sh https://download.bt.cn/install/install_6.0.sh && sh install.sh ed8484bec

# 安装完成后访问面板
# http://your-server-ip:8888
```

### LNMP环境配置
```bash
# 在宝塔面板中安装以下软件
# - Nginx 1.22+
# - MySQL 5.7 (宝塔最高支持版本)
# - PHP 8.1+ (用于宝塔面板管理)
# - Node.js 18+ (手动安装)
# - PM2 (Node.js进程管理)
```

### Node.js 环境安装
```bash
# 安装Node.js 18 LTS
curl -fsSL https://rpm.nodesource.com/setup_18.x | sudo bash -
sudo yum install -y nodejs

# 验证安装
node --version
npm --version

# 安装PM2
npm install -g pm2

# 安装pnpm (推荐)
npm install -g pnpm
```

## 项目部署配置

### 目录结构规划
```
/www/wwwroot/yourpos.com/
├── frontend/                 # Nuxt.js前端项目
├── backend/                  # Express后端API
├── admin/                    # Vue管理后台
├── installer/                # 安装向导
├── uploads/                  # 文件上传目录
├── logs/                     # 日志目录
├── ssl/                      # SSL证书
└── scripts/                  # 部署脚本
```

### Nginx配置
```nginx
# /www/server/panel/vhost/nginx/yourpos.com.conf
server {
    listen 80;
    server_name yourpos.com www.yourpos.com;
    return 301 https://$server_name$request_uri;
}

server {
    listen 443 ssl http2;
    server_name yourpos.com www.yourpos.com;
    
    # SSL证书配置
    ssl_certificate /www/wwwroot/yourpos.com/ssl/fullchain.pem;
    ssl_certificate_key /www/wwwroot/yourpos.com/ssl/privkey.pem;
    ssl_protocols TLSv1.2 TLSv1.3;
    ssl_ciphers ECDHE-RSA-AES128-GCM-SHA256:ECDHE:ECDH:AES:HIGH:!NULL:!aNULL:!MD5:!ADH:!RC4;
    ssl_prefer_server_ciphers on;
    
    # 安全头
    add_header X-Frame-Options DENY;
    add_header X-Content-Type-Options nosniff;
    add_header X-XSS-Protection "1; mode=block";
    add_header Strict-Transport-Security "max-age=31536000; includeSubDomains" always;
    
    # Gzip压缩
    gzip on;
    gzip_vary on;
    gzip_min_length 1024;
    gzip_types text/plain text/css text/xml text/javascript application/javascript application/xml+rss application/json;
    
    # 前端静态文件
    location / {
        proxy_pass http://127.0.0.1:3000;
        proxy_http_version 1.1;
        proxy_set_header Upgrade $http_upgrade;
        proxy_set_header Connection 'upgrade';
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        proxy_cache_bypass $http_upgrade;
        
        # 缓存静态资源
        location ~* \.(js|css|png|jpg|jpeg|gif|ico|svg|woff|woff2|ttf|eot)$ {
            expires 1y;
            add_header Cache-Control "public, immutable";
        }
    }
    
    # API接口
    location /api/ {
        proxy_pass http://127.0.0.1:3001;
        proxy_http_version 1.1;
        proxy_set_header Upgrade $http_upgrade;
        proxy_set_header Connection 'upgrade';
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        proxy_cache_bypass $http_upgrade;
    }
    
    # 管理后台
    location /admin/ {
        proxy_pass http://127.0.0.1:3002;
        proxy_http_version 1.1;
        proxy_set_header Upgrade $http_upgrade;
        proxy_set_header Connection 'upgrade';
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        proxy_cache_bypass $http_upgrade;
    }
    
    # 文件上传目录
    location /uploads/ {
        alias /www/wwwroot/yourpos.com/uploads/;
        expires 1y;
        add_header Cache-Control "public";
    }
    
    # 安装向导
    location /install/ {
        proxy_pass http://127.0.0.1:3003;
        proxy_http_version 1.1;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
    }
    
    # 禁止访问敏感文件
    location ~ /\. {
        deny all;
    }
    
    location ~ \.(env|log|sql)$ {
        deny all;
    }
}
```

### PM2配置
```javascript
// ecosystem.config.js
module.exports = {
  apps: [
    {
      name: 'pos-frontend',
      script: '.output/server/index.mjs',
      cwd: '/www/wwwroot/yourpos.com/frontend',
      instances: 'max',
      exec_mode: 'cluster',
      env: {
        NODE_ENV: 'production',
        PORT: 3000,
        NITRO_HOST: '127.0.0.1',
        NITRO_PORT: 3000
      },
      error_file: '/www/wwwroot/yourpos.com/logs/frontend-error.log',
      out_file: '/www/wwwroot/yourpos.com/logs/frontend-out.log',
      log_file: '/www/wwwroot/yourpos.com/logs/frontend.log',
      time: true
    },
    {
      name: 'pos-backend',
      script: 'dist/index.js',
      cwd: '/www/wwwroot/yourpos.com/backend',
      instances: 2,
      exec_mode: 'cluster',
      env: {
        NODE_ENV: 'production',
        PORT: 3001,
        HOST: '127.0.0.1'
      },
      error_file: '/www/wwwroot/yourpos.com/logs/backend-error.log',
      out_file: '/www/wwwroot/yourpos.com/logs/backend-out.log',
      log_file: '/www/wwwroot/yourpos.com/logs/backend.log',
      time: true
    },
    {
      name: 'pos-admin',
      script: 'dist/index.js',
      cwd: '/www/wwwroot/yourpos.com/admin',
      instances: 1,
      env: {
        NODE_ENV: 'production',
        PORT: 3002,
        HOST: '127.0.0.1'
      },
      error_file: '/www/wwwroot/yourpos.com/logs/admin-error.log',
      out_file: '/www/wwwroot/yourpos.com/logs/admin-out.log',
      log_file: '/www/wwwroot/yourpos.com/logs/admin.log',
      time: true
    }
  ]
};
```

## 一键安装向导

### 安装向导界面
```vue
<!-- installer/web/pages/index.vue -->
<template>
  <div class="installer-container">
    <div class="installer-card">
      <h1 class="installer-title">POS网站安装向导</h1>
      
      <div class="step-indicator">
        <div
          v-for="(step, index) in steps"
          :key="index"
          :class="['step', { active: currentStep === index, completed: currentStep > index }]"
        >
          {{ step.title }}
        </div>
      </div>
      
      <div class="step-content">
        <!-- 环境检测 -->
        <div v-if="currentStep === 0" class="step-panel">
          <h2>环境检测</h2>
          <div class="check-list">
            <div
              v-for="check in environmentChecks"
              :key="check.name"
              :class="['check-item', check.status]"
            >
              <span class="check-name">{{ check.name }}</span>
              <span class="check-status">{{ check.message }}</span>
            </div>
          </div>
        </div>
        
        <!-- 数据库配置 -->
        <div v-if="currentStep === 1" class="step-panel">
          <h2>数据库配置</h2>
          <form @submit.prevent="testDatabase">
            <div class="form-group">
              <label>数据库主机</label>
              <input v-model="dbConfig.host" type="text" placeholder="localhost" />
            </div>
            <div class="form-group">
              <label>数据库端口</label>
              <input v-model="dbConfig.port" type="number" placeholder="3306" />
            </div>
            <div class="form-group">
              <label>数据库名称</label>
              <input v-model="dbConfig.database" type="text" placeholder="pos_website" />
            </div>
            <div class="form-group">
              <label>用户名</label>
              <input v-model="dbConfig.username" type="text" placeholder="root" />
            </div>
            <div class="form-group">
              <label>密码</label>
              <input v-model="dbConfig.password" type="password" />
            </div>
            <button type="submit" class="test-btn">测试连接</button>
          </form>
        </div>
        
        <!-- 站点配置 -->
        <div v-if="currentStep === 2" class="step-panel">
          <h2>站点配置</h2>
          <form>
            <div class="form-group">
              <label>站点名称</label>
              <input v-model="siteConfig.name" type="text" placeholder="POS机产品展示网站" />
            </div>
            <div class="form-group">
              <label>站点域名</label>
              <input v-model="siteConfig.url" type="text" placeholder="https://www.yourpos.com" />
            </div>
            <div class="form-group">
              <label>管理员邮箱</label>
              <input v-model="siteConfig.adminEmail" type="email" placeholder="<EMAIL>" />
            </div>
            <div class="form-group">
              <label>管理员密码</label>
              <input v-model="siteConfig.adminPassword" type="password" />
            </div>
          </form>
        </div>
        
        <!-- 安装进度 -->
        <div v-if="currentStep === 3" class="step-panel">
          <h2>正在安装...</h2>
          <div class="progress-container">
            <div class="progress-bar">
              <div
                class="progress-fill"
                :style="{ width: `${installProgress}%` }"
              ></div>
            </div>
            <div class="progress-text">{{ installMessage }}</div>
          </div>
          <div class="install-logs">
            <div
              v-for="(log, index) in installLogs"
              :key="index"
              :class="['log-item', log.type]"
            >
              {{ log.message }}
            </div>
          </div>
        </div>
        
        <!-- 安装完成 -->
        <div v-if="currentStep === 4" class="step-panel">
          <h2>安装完成！</h2>
          <div class="success-message">
            <p>恭喜！您的POS网站已成功安装。</p>
            <div class="site-links">
              <a :href="siteConfig.url" target="_blank" class="link-btn">访问网站</a>
              <a :href="`${siteConfig.url}/admin`" target="_blank" class="link-btn">管理后台</a>
            </div>
          </div>
        </div>
      </div>
      
      <div class="step-actions">
        <button
          v-if="currentStep > 0 && currentStep < 4"
          @click="previousStep"
          class="btn btn-secondary"
        >
          上一步
        </button>
        <button
          v-if="currentStep < 3"
          @click="nextStep"
          :disabled="!canProceed"
          class="btn btn-primary"
        >
          下一步
        </button>
        <button
          v-if="currentStep === 3"
          @click="startInstall"
          :disabled="installing"
          class="btn btn-primary"
        >
          开始安装
        </button>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
// 安装向导逻辑
const currentStep = ref(0);
const installing = ref(false);
const installProgress = ref(0);
const installMessage = ref('');
const installLogs = ref<Array<{ message: string; type: string }>>([]);

const steps = [
  { title: '环境检测' },
  { title: '数据库配置' },
  { title: '站点配置' },
  { title: '安装进度' },
  { title: '安装完成' }
];

const environmentChecks = ref([
  { name: 'Node.js 版本', status: 'checking', message: '检测中...' },
  { name: 'MySQL 连接', status: 'checking', message: '检测中...' },
  { name: '文件权限', status: 'checking', message: '检测中...' },
  { name: '端口可用性', status: 'checking', message: '检测中...' }
]);

const dbConfig = ref({
  host: 'localhost',
  port: 3306,
  database: 'pos_website',
  username: 'root',
  password: ''
});

const siteConfig = ref({
  name: 'POS机产品展示网站',
  url: '',
  adminEmail: '',
  adminPassword: ''
});

// 检测环境
const checkEnvironment = async () => {
  try {
    const response = await $fetch('/api/install/check-environment');
    environmentChecks.value = response.checks;
  } catch (error) {
    console.error('环境检测失败:', error);
  }
};

// 测试数据库连接
const testDatabase = async () => {
  try {
    const response = await $fetch('/api/install/test-database', {
      method: 'POST',
      body: dbConfig.value
    });
    
    if (response.success) {
      alert('数据库连接成功！');
    } else {
      alert('数据库连接失败：' + response.message);
    }
  } catch (error) {
    alert('数据库连接失败');
  }
};

// 开始安装
const startInstall = async () => {
  installing.value = true;
  installProgress.value = 0;
  installLogs.value = [];
  
  try {
    const response = await $fetch('/api/install/start', {
      method: 'POST',
      body: {
        database: dbConfig.value,
        site: siteConfig.value
      }
    });
    
    // 监听安装进度
    const eventSource = new EventSource('/api/install/progress');
    
    eventSource.onmessage = (event) => {
      const data = JSON.parse(event.data);
      installProgress.value = data.progress;
      installMessage.value = data.message;
      
      installLogs.value.push({
        message: data.message,
        type: data.type || 'info'
      });
      
      if (data.progress === 100) {
        eventSource.close();
        currentStep.value = 4;
        installing.value = false;
      }
    };
    
    eventSource.onerror = () => {
      eventSource.close();
      installing.value = false;
      alert('安装过程中出现错误');
    };
    
  } catch (error) {
    installing.value = false;
    alert('安装失败');
  }
};

const canProceed = computed(() => {
  switch (currentStep.value) {
    case 0:
      return environmentChecks.value.every(check => check.status === 'success');
    case 1:
      return dbConfig.value.host && dbConfig.value.database && dbConfig.value.username;
    case 2:
      return siteConfig.value.name && siteConfig.value.url && siteConfig.value.adminEmail && siteConfig.value.adminPassword;
    default:
      return true;
  }
});

const nextStep = () => {
  if (canProceed.value && currentStep.value < 4) {
    currentStep.value++;
  }
};

const previousStep = () => {
  if (currentStep.value > 0) {
    currentStep.value--;
  }
};

// 初始化
onMounted(() => {
  checkEnvironment();
});
</script>
```

### 安装脚本
```typescript
// installer/scripts/install.ts
import { exec } from 'child_process';
import { promisify } from 'util';
import fs from 'fs/promises';
import path from 'path';
import mysql from 'mysql2/promise';

const execAsync = promisify(exec);

export class Installer {
  private progress = 0;
  private progressCallback?: (progress: number, message: string) => void;

  constructor(progressCallback?: (progress: number, message: string) => void) {
    this.progressCallback = progressCallback;
  }

  /**
   * 更新安装进度
   */
  private updateProgress(progress: number, message: string) {
    this.progress = progress;
    if (this.progressCallback) {
      this.progressCallback(progress, message);
    }
  }

  /**
   * 检查环境
   */
  async checkEnvironment() {
    const checks = [];

    // 检查Node.js版本
    try {
      const { stdout } = await execAsync('node --version');
      const version = stdout.trim();
      const majorVersion = parseInt(version.substring(1).split('.')[0]);
      
      checks.push({
        name: 'Node.js 版本',
        status: majorVersion >= 18 ? 'success' : 'error',
        message: majorVersion >= 18 ? `${version} ✓` : `${version} (需要 18+)`
      });
    } catch (error) {
      checks.push({
        name: 'Node.js 版本',
        status: 'error',
        message: '未安装 Node.js'
      });
    }

    // 检查MySQL
    try {
      await execAsync('mysql --version');
      checks.push({
        name: 'MySQL',
        status: 'success',
        message: '已安装 ✓'
      });
    } catch (error) {
      checks.push({
        name: 'MySQL',
        status: 'error',
        message: '未安装 MySQL'
      });
    }

    // 检查文件权限
    try {
      await fs.access('/www/wwwroot', fs.constants.W_OK);
      checks.push({
        name: '文件权限',
        status: 'success',
        message: '写入权限正常 ✓'
      });
    } catch (error) {
      checks.push({
        name: '文件权限',
        status: 'error',
        message: '缺少写入权限'
      });
    }

    // 检查端口
    const ports = [3000, 3001, 3002];
    for (const port of ports) {
      try {
        await execAsync(`netstat -tuln | grep :${port}`);
        checks.push({
          name: `端口 ${port}`,
          status: 'warning',
          message: '端口已被占用'
        });
      } catch (error) {
        checks.push({
          name: `端口 ${port}`,
          status: 'success',
          message: '端口可用 ✓'
        });
      }
    }

    return { checks };
  }

  /**
   * 测试数据库连接
   */
  async testDatabase(config: any) {
    try {
      const connection = await mysql.createConnection({
        host: config.host,
        port: config.port,
        user: config.username,
        password: config.password
      });

      await connection.execute('SELECT 1');
      await connection.end();

      return { success: true, message: '连接成功' };
    } catch (error) {
      return { 
        success: false, 
        message: error instanceof Error ? error.message : '连接失败' 
      };
    }
  }

  /**
   * 开始安装
   */
  async startInstall(config: { database: any; site: any }) {
    try {
      this.updateProgress(0, '开始安装...');

      // 1. 创建数据库
      this.updateProgress(10, '创建数据库...');
      await this.createDatabase(config.database);

      // 2. 安装依赖
      this.updateProgress(20, '安装前端依赖...');
      await this.installDependencies('frontend');

      this.updateProgress(40, '安装后端依赖...');
      await this.installDependencies('backend');

      this.updateProgress(60, '安装管理后台依赖...');
      await this.installDependencies('admin');

      // 3. 构建项目
      this.updateProgress(70, '构建前端项目...');
      await this.buildProject('frontend');

      this.updateProgress(80, '构建后端项目...');
      await this.buildProject('backend');

      this.updateProgress(85, '构建管理后台...');
      await this.buildProject('admin');

      // 4. 生成配置文件
      this.updateProgress(90, '生成配置文件...');
      await this.generateConfig(config);

      // 5. 启动服务
      this.updateProgress(95, '启动服务...');
      await this.startServices();

      this.updateProgress(100, '安装完成！');

      return { success: true };
    } catch (error) {
      throw new Error(`安装失败: ${error instanceof Error ? error.message : '未知错误'}`);
    }
  }

  /**
   * 创建数据库
   */
  private async createDatabase(config: any) {
    const connection = await mysql.createConnection({
      host: config.host,
      port: config.port,
      user: config.username,
      password: config.password
    });

    await connection.execute(`CREATE DATABASE IF NOT EXISTS \`${config.database}\` CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci`);
    await connection.end();
  }

  /**
   * 安装依赖
   */
  private async installDependencies(project: string) {
    const projectPath = `/www/wwwroot/yourpos.com/${project}`;
    await execAsync('pnpm install', { cwd: projectPath });
  }

  /**
   * 构建项目
   */
  private async buildProject(project: string) {
    const projectPath = `/www/wwwroot/yourpos.com/${project}`;
    
    if (project === 'frontend') {
      await execAsync('pnpm build', { cwd: projectPath });
    } else {
      await execAsync('pnpm build', { cwd: projectPath });
    }
  }

  /**
   * 生成配置文件
   */
  private async generateConfig(config: any) {
    // 生成环境变量文件
    const envContent = `
# 数据库配置
DATABASE_URL="mysql://${config.database.username}:${config.database.password}@${config.database.host}:${config.database.port}/${config.database.database}"

# 站点配置
SITE_URL="${config.site.url}"
SITE_NAME="${config.site.name}"

# JWT密钥
JWT_SECRET="${this.generateRandomString(32)}"

# 文件上传
UPLOAD_PATH="/www/wwwroot/yourpos.com/uploads"

# 日志配置
LOG_LEVEL="info"
LOG_PATH="/www/wwwroot/yourpos.com/logs"
`;

    await fs.writeFile('/www/wwwroot/yourpos.com/.env', envContent);
  }

  /**
   * 启动服务
   */
  private async startServices() {
    await execAsync('pm2 start ecosystem.config.js', {
      cwd: '/www/wwwroot/yourpos.com'
    });
  }

  /**
   * 生成随机字符串
   */
  private generateRandomString(length: number): string {
    const chars = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789';
    let result = '';
    for (let i = 0; i < length; i++) {
      result += chars.charAt(Math.floor(Math.random() * chars.length));
    }
    return result;
  }
}
```

## 自动化部署脚本

### 部署脚本
```bash
#!/bin/bash
# deployment/scripts/deploy.sh

set -e

# 配置变量
PROJECT_NAME="pos-website"
PROJECT_PATH="/www/wwwroot/yourpos.com"
BACKUP_PATH="/www/backup/pos-website"
GIT_REPO="https://github.com/your-username/pos-website.git"

echo "开始部署 $PROJECT_NAME..."

# 创建备份
echo "创建备份..."
mkdir -p $BACKUP_PATH
tar -czf "$BACKUP_PATH/backup-$(date +%Y%m%d-%H%M%S).tar.gz" -C $PROJECT_PATH .

# 拉取最新代码
echo "拉取最新代码..."
cd $PROJECT_PATH
git pull origin main

# 安装依赖
echo "安装前端依赖..."
cd $PROJECT_PATH/frontend
pnpm install

echo "安装后端依赖..."
cd $PROJECT_PATH/backend
pnpm install

echo "安装管理后台依赖..."
cd $PROJECT_PATH/admin
pnpm install

# 构建项目
echo "构建前端项目..."
cd $PROJECT_PATH/frontend
pnpm build

echo "构建后端项目..."
cd $PROJECT_PATH/backend
pnpm build

echo "构建管理后台..."
cd $PROJECT_PATH/admin
pnpm build

# 数据库迁移
echo "执行数据库迁移..."
cd $PROJECT_PATH/backend
pnpm prisma migrate deploy

# 重启服务
echo "重启服务..."
pm2 restart ecosystem.config.js

echo "部署完成！"
```

### 监控脚本
```bash
#!/bin/bash
# deployment/scripts/monitor.sh

# 检查服务状态
check_service() {
    local service_name=$1
    local port=$2
    
    if pm2 list | grep -q "$service_name.*online"; then
        echo "✓ $service_name 运行正常"
    else
        echo "✗ $service_name 服务异常"
        pm2 restart $service_name
    fi
    
    if curl -f -s "http://localhost:$port" > /dev/null; then
        echo "✓ $service_name 端口 $port 响应正常"
    else
        echo "✗ $service_name 端口 $port 无响应"
    fi
}

echo "检查服务状态..."
check_service "pos-frontend" 3000
check_service "pos-backend" 3001
check_service "pos-admin" 3002

# 检查磁盘空间
disk_usage=$(df -h / | awk 'NR==2 {print $5}' | sed 's/%//')
if [ $disk_usage -gt 80 ]; then
    echo "⚠ 磁盘使用率过高: ${disk_usage}%"
fi

# 检查内存使用
memory_usage=$(free | grep Mem | awk '{printf("%.1f", $3/$2 * 100.0)}')
if (( $(echo "$memory_usage > 80" | bc -l) )); then
    echo "⚠ 内存使用率过高: ${memory_usage}%"
fi

echo "监控检查完成"
```

