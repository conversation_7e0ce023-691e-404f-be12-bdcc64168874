/**
 * 共享常量定义
 */

// API 路径常量
export const API_ROUTES = {
  // 认证相关
  AUTH: {
    LOGIN: '/api/auth/login',
    REGISTER: '/api/auth/register',
    LOGOUT: '/api/auth/logout',
    REFRESH: '/api/auth/refresh',
    PROFILE: '/api/auth/profile'
  },
  
  // 产品相关
  PRODUCTS: {
    LIST: '/api/products',
    DETAIL: (slug: string) => `/api/products/${slug}`,
    CREATE: '/api/products',
    UPDATE: (id: number) => `/api/products/${id}`,
    DELETE: (id: number) => `/api/products/${id}`,
    SEARCH: '/api/products/search',
    FEATURED: '/api/products/featured',
    POPULAR: '/api/products/popular',
    RELATED: (id: number) => `/api/products/${id}/related`
  },
  
  // 分类相关
  CATEGORIES: {
    LIST: '/api/categories',
    DETAIL: (slug: string) => `/api/categories/${slug}`,
    CREATE: '/api/categories',
    UPDATE: (id: number) => `/api/categories/${id}`,
    DELETE: (id: number) => `/api/categories/${id}`,
    TREE: '/api/categories/tree'
  },
  
  // 文章相关
  ARTICLES: {
    LIST: '/api/articles',
    DETAIL: (slug: string) => `/api/articles/${slug}`,
    CREATE: '/api/articles',
    UPDATE: (id: number) => `/api/articles/${id}`,
    DELETE: (id: number) => `/api/articles/${id}`,
    PUBLISH: (id: number) => `/api/articles/${id}/publish`,
    UNPUBLISH: (id: number) => `/api/articles/${id}/unpublish`
  },
  
  // 公司信息
  COMPANY: {
    INFO: '/api/company/info',
    UPDATE: '/api/company/info'
  },
  
  // 联系咨询
  CONTACT: {
    INQUIRIES: '/api/contact/inquiries',
    SUBMIT: '/api/contact/submit',
    REPLY: (id: number) => `/api/contact/inquiries/${id}/reply`
  },
  
  // 文件上传
  UPLOAD: {
    IMAGE: '/api/upload/image',
    FILE: '/api/upload/file',
    MULTIPLE: '/api/upload/multiple'
  },
  
  // 统计分析
  ANALYTICS: {
    OVERVIEW: '/api/analytics/overview',
    VISITORS: '/api/analytics/visitors',
    PAGES: '/api/analytics/pages',
    TRACK: '/api/analytics/track'
  },
  
  // 系统设置
  SETTINGS: {
    LIST: '/api/settings',
    UPDATE: '/api/settings',
    GET: (key: string) => `/api/settings/${key}`
  },
  
  // SEO相关
  SEO: {
    SITEMAP: '/sitemap.xml',
    ROBOTS: '/robots.txt'
  }
} as const;

// 默认分页配置
export const PAGINATION = {
  DEFAULT_PAGE: 1,
  DEFAULT_LIMIT: 10,
  MAX_LIMIT: 100,
  PRODUCTS_PER_PAGE: 12,
  ARTICLES_PER_PAGE: 10,
  ADMIN_PER_PAGE: 20
} as const;

// 文件上传配置
export const UPLOAD_CONFIG = {
  MAX_FILE_SIZE: 10 * 1024 * 1024, // 10MB
  MAX_IMAGE_SIZE: 5 * 1024 * 1024,  // 5MB
  ALLOWED_IMAGE_TYPES: ['image/jpeg', 'image/png', 'image/webp', 'image/gif'],
  ALLOWED_FILE_TYPES: [
    'application/pdf',
    'application/msword',
    'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
    'application/vnd.ms-excel',
    'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
  ],
  IMAGE_QUALITY: 85,
  THUMBNAIL_SIZE: { width: 300, height: 300 },
  MEDIUM_SIZE: { width: 800, height: 600 },
  LARGE_SIZE: { width: 1200, height: 900 }
} as const;

// 缓存配置
export const CACHE_CONFIG = {
  TTL: {
    SHORT: 5 * 60,      // 5分钟
    MEDIUM: 30 * 60,    // 30分钟
    LONG: 60 * 60,      // 1小时
    VERY_LONG: 24 * 60 * 60  // 24小时
  },
  KEYS: {
    PRODUCTS: 'products',
    CATEGORIES: 'categories',
    ARTICLES: 'articles',
    COMPANY_INFO: 'company_info',
    SETTINGS: 'settings',
    ANALYTICS: 'analytics'
  }
} as const;

// 错误代码
export const ERROR_CODES = {
  // 通用错误
  INTERNAL_SERVER_ERROR: 'INTERNAL_SERVER_ERROR',
  BAD_REQUEST: 'BAD_REQUEST',
  UNAUTHORIZED: 'UNAUTHORIZED',
  FORBIDDEN: 'FORBIDDEN',
  NOT_FOUND: 'NOT_FOUND',
  VALIDATION_ERROR: 'VALIDATION_ERROR',
  
  // 认证错误
  INVALID_CREDENTIALS: 'INVALID_CREDENTIALS',
  TOKEN_EXPIRED: 'TOKEN_EXPIRED',
  TOKEN_INVALID: 'TOKEN_INVALID',
  
  // 业务错误
  EMAIL_ALREADY_EXISTS: 'EMAIL_ALREADY_EXISTS',
  PRODUCT_NOT_FOUND: 'PRODUCT_NOT_FOUND',
  CATEGORY_NOT_FOUND: 'CATEGORY_NOT_FOUND',
  INSUFFICIENT_STOCK: 'INSUFFICIENT_STOCK',
  
  // 文件上传错误
  FILE_TOO_LARGE: 'FILE_TOO_LARGE',
  INVALID_FILE_TYPE: 'INVALID_FILE_TYPE',
  UPLOAD_FAILED: 'UPLOAD_FAILED'
} as const;

// HTTP状态码
export const HTTP_STATUS = {
  OK: 200,
  CREATED: 201,
  NO_CONTENT: 204,
  BAD_REQUEST: 400,
  UNAUTHORIZED: 401,
  FORBIDDEN: 403,
  NOT_FOUND: 404,
  CONFLICT: 409,
  UNPROCESSABLE_ENTITY: 422,
  INTERNAL_SERVER_ERROR: 500
} as const;

// 用户角色权限
export const PERMISSIONS = {
  ADMIN: [
    'users.read',
    'users.write',
    'users.delete',
    'products.read',
    'products.write',
    'products.delete',
    'categories.read',
    'categories.write',
    'categories.delete',
    'articles.read',
    'articles.write',
    'articles.delete',
    'articles.publish',
    'company.read',
    'company.write',
    'settings.read',
    'settings.write',
    'analytics.read'
  ],
  EDITOR: [
    'products.read',
    'products.write',
    'categories.read',
    'categories.write',
    'articles.read',
    'articles.write',
    'company.read',
    'analytics.read'
  ],
  USER: [
    'products.read',
    'articles.read',
    'company.read'
  ]
} as const;

// 产品相关常量
export const PRODUCT_CONFIG = {
  MIN_PRICE: 0,
  MAX_PRICE: 999999,
  DEFAULT_STOCK: 0,
  MIN_STOCK_WARNING: 10,
  MAX_IMAGES: 10,
  MAX_SPECIFICATIONS: 20,
  SLUG_MAX_LENGTH: 255,
  NAME_MAX_LENGTH: 255,
  DESCRIPTION_MAX_LENGTH: 5000
} as const;

// SEO相关常量
export const SEO_CONFIG = {
  TITLE_MAX_LENGTH: 60,
  DESCRIPTION_MAX_LENGTH: 160,
  KEYWORDS_MAX_LENGTH: 500,
  DEFAULT_TITLE: 'POS机产品展示网站',
  DEFAULT_DESCRIPTION: '专业的POS机产品展示平台，提供最新的支付终端设备信息',
  DEFAULT_KEYWORDS: 'POS机,支付终端,移动支付,智能POS,收银设备',
  SITEMAP_PRIORITY: {
    HOME: 1.0,
    PRODUCTS: 0.9,
    CATEGORIES: 0.8,
    ARTICLES: 0.7,
    PAGES: 0.6
  },
  CHANGE_FREQUENCY: {
    DAILY: 'daily',
    WEEKLY: 'weekly',
    MONTHLY: 'monthly',
    YEARLY: 'yearly'
  }
} as const;

// 邮件模板
export const EMAIL_TEMPLATES = {
  CONTACT_INQUIRY: 'contact-inquiry',
  WELCOME: 'welcome',
  PASSWORD_RESET: 'password-reset',
  ORDER_CONFIRMATION: 'order-confirmation'
} as const;

// 日志级别
export const LOG_LEVELS = {
  ERROR: 'error',
  WARN: 'warn',
  INFO: 'info',
  DEBUG: 'debug'
} as const;

// 系统配置键
export const SYSTEM_SETTINGS = {
  SITE_NAME: 'site_name',
  SITE_DESCRIPTION: 'site_description',
  SITE_KEYWORDS: 'site_keywords',
  SITE_LOGO: 'site_logo',
  CONTACT_EMAIL: 'contact_email',
  CONTACT_PHONE: 'contact_phone',
  CONTACT_ADDRESS: 'contact_address',
  SOCIAL_FACEBOOK: 'social_facebook',
  SOCIAL_TWITTER: 'social_twitter',
  SOCIAL_LINKEDIN: 'social_linkedin',
  ANALYTICS_GA_ID: 'analytics_ga_id',
  SMTP_HOST: 'smtp_host',
  SMTP_PORT: 'smtp_port',
  SMTP_USER: 'smtp_user',
  SMTP_PASS: 'smtp_pass'
} as const;

// 正则表达式
export const REGEX = {
  EMAIL: /^[^\s@]+@[^\s@]+\.[^\s@]+$/,
  PHONE: /^1[3-9]\d{9}$/,
  PASSWORD: /^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)[a-zA-Z\d@$!%*?&]{8,}$/,
  SLUG: /^[a-z0-9]+(?:-[a-z0-9]+)*$/,
  URL: /^https?:\/\/.+/,
  COLOR: /^#([A-Fa-f0-9]{6}|[A-Fa-f0-9]{3})$/
} as const;

// 时间格式
export const DATE_FORMATS = {
  DATE: 'YYYY-MM-DD',
  DATETIME: 'YYYY-MM-DD HH:mm:ss',
  TIME: 'HH:mm:ss',
  DISPLAY_DATE: 'YYYY年MM月DD日',
  DISPLAY_DATETIME: 'YYYY年MM月DD日 HH:mm'
} as const;
