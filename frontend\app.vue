<template>
  <div id="app">
    <!-- 全局加载指示器 -->
    <LazyGlobalLoading v-if="pending" />
    
    <!-- 主要内容 -->
    <NuxtLayout>
      <NuxtPage />
    </NuxtLayout>
    
    <!-- 全局通知 -->
    <LazyGlobalNotification />
    
    <!-- 返回顶部按钮 -->
    <LazyGlobalBackToTop />
  </div>
</template>

<script setup lang="ts">
// 设置页面元信息
useHead({
  htmlAttrs: {
    lang: 'zh-CN'
  },
  meta: [
    { charset: 'utf-8' },
    { name: 'viewport', content: 'width=device-width, initial-scale=1' },
    { name: 'format-detection', content: 'telephone=no' }
  ]
})

// 全局加载状态
const { pending } = useLazyAsyncData('app-init', async () => {
  // 这里可以进行应用初始化操作
  // 比如获取全局配置、用户信息等
  return true
})

// 监听路由变化，用于页面访问统计
const route = useRoute()
const { $analytics } = useNuxtApp()

watch(() => route.fullPath, (newPath) => {
  // 页面访问统计
  if (process.client && $analytics) {
    $analytics.trackPageView(newPath)
  }
}, { immediate: true })

// 全局错误处理
onErrorCaptured((error) => {
  console.error('Global error captured:', error)
  // 这里可以发送错误到监控服务
  return false
})
</script>

<style>
/* 全局样式已在 assets/css/main.css 中定义 */
</style>
