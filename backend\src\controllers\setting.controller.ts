/**
 * 系统设置控制器
 */

import { Request, Response } from 'express';
import { DatabaseManager } from '../config/database';
import { logger } from '../utils/logger';
import { ApiResponse } from '../utils/response';
import { NotFoundError, ValidationError } from '../middleware/errorHandler';

/**
 * 系统设置控制器
 */
export class SettingController {
  private prisma = DatabaseManager.getInstance().getPrisma();

  /**
   * 获取所有设置
   */
  public async getSettings(req: Request, res: Response): Promise<void> {
    const { category, public: isPublic } = req.query;

    try {
      const where: any = {};

      if (category && typeof category === 'string') {
        where.category = category;
      }

      if (isPublic === 'true') {
        where.isPublic = true;
      }

      const settings = await this.prisma.setting.findMany({
        where,
        orderBy: [
          { category: 'asc' },
          { key: 'asc' },
        ],
      });

      // 按分类分组
      const groupedSettings = settings.reduce((acc, setting) => {
        const category = setting.category || 'general';
        if (!acc[category]) {
          acc[category] = [];
        }
        acc[category].push(setting);
        return acc;
      }, {} as Record<string, typeof settings>);

      res.json(ApiResponse.success(groupedSettings));
    } catch (error) {
      logger.error('获取系统设置失败', { error });
      throw error;
    }
  }

  /**
   * 获取公开设置
   */
  public async getPublicSettings(req: Request, res: Response): Promise<void> {
    try {
      const settings = await this.prisma.setting.findMany({
        where: { isPublic: true },
        select: {
          key: true,
          value: true,
          category: true,
        },
      });

      // 转换为键值对格式
      const settingsMap = settings.reduce((acc, setting) => {
        acc[setting.key] = setting.value;
        return acc;
      }, {} as Record<string, string>);

      res.json(ApiResponse.success(settingsMap));
    } catch (error) {
      logger.error('获取公开设置失败', { error });
      throw error;
    }
  }

  /**
   * 获取单个设置
   */
  public async getSetting(req: Request, res: Response): Promise<void> {
    const { key } = req.params;

    try {
      const setting = await this.prisma.setting.findUnique({
        where: { key },
      });

      if (!setting) {
        throw new NotFoundError('设置');
      }

      res.json(ApiResponse.success(setting));
    } catch (error) {
      logger.error('获取设置失败', { error, key });
      throw error;
    }
  }

  /**
   * 创建设置
   */
  public async createSetting(req: Request, res: Response): Promise<void> {
    const { key, value, description, category, isPublic } = req.body;

    try {
      // 检查设置是否已存在
      const existingSetting = await this.prisma.setting.findUnique({
        where: { key },
      });

      if (existingSetting) {
        throw new ValidationError('设置键已存在');
      }

      const setting = await this.prisma.setting.create({
        data: {
          key,
          value,
          description,
          category: category || 'general',
          isPublic: isPublic || false,
        },
      });

      logger.info('系统设置创建成功', {
        key,
        category: setting.category,
        userId: req.user?.id,
      });

      res.status(201).json(ApiResponse.created(setting));
    } catch (error) {
      logger.error('创建系统设置失败', { error, data: req.body });
      throw error;
    }
  }

  /**
   * 更新设置
   */
  public async updateSetting(req: Request, res: Response): Promise<void> {
    const { key } = req.params;
    const { value, description, category, isPublic } = req.body;

    try {
      const existingSetting = await this.prisma.setting.findUnique({
        where: { key },
      });

      if (!existingSetting) {
        throw new NotFoundError('设置');
      }

      const setting = await this.prisma.setting.update({
        where: { key },
        data: {
          ...(value !== undefined && { value }),
          ...(description !== undefined && { description }),
          ...(category !== undefined && { category }),
          ...(isPublic !== undefined && { isPublic }),
        },
      });

      logger.info('系统设置更新成功', {
        key,
        userId: req.user?.id,
        changes: Object.keys(req.body),
      });

      res.json(ApiResponse.updated(setting));
    } catch (error) {
      logger.error('更新系统设置失败', { error, key, data: req.body });
      throw error;
    }
  }

  /**
   * 批量更新设置
   */
  public async batchUpdateSettings(req: Request, res: Response): Promise<void> {
    const settings = req.body;

    if (!settings || typeof settings !== 'object') {
      throw new ValidationError('请提供有效的设置数据');
    }

    try {
      const updatePromises = Object.entries(settings).map(async ([key, value]) => {
        return this.prisma.setting.upsert({
          where: { key },
          update: { value: String(value) },
          create: {
            key,
            value: String(value),
            category: 'general',
            isPublic: false,
          },
        });
      });

      const updatedSettings = await Promise.all(updatePromises);

      logger.info('批量更新系统设置成功', {
        count: updatedSettings.length,
        keys: Object.keys(settings),
        userId: req.user?.id,
      });

      res.json(ApiResponse.success(updatedSettings, '批量更新成功'));
    } catch (error) {
      logger.error('批量更新系统设置失败', { error, data: req.body });
      throw error;
    }
  }

  /**
   * 删除设置
   */
  public async deleteSetting(req: Request, res: Response): Promise<void> {
    const { key } = req.params;

    try {
      const setting = await this.prisma.setting.findUnique({
        where: { key },
      });

      if (!setting) {
        throw new NotFoundError('设置');
      }

      await this.prisma.setting.delete({
        where: { key },
      });

      logger.info('系统设置删除成功', {
        key,
        userId: req.user?.id,
      });

      res.json(ApiResponse.deleted());
    } catch (error) {
      logger.error('删除系统设置失败', { error, key });
      throw error;
    }
  }

  /**
   * 重置设置为默认值
   */
  public async resetSettings(req: Request, res: Response): Promise<void> {
    const { category } = req.body;

    try {
      // 获取默认设置
      const defaultSettings = this.getDefaultSettings();

      let settingsToReset = defaultSettings;
      if (category) {
        settingsToReset = defaultSettings.filter(setting => setting.category === category);
      }

      // 批量更新设置
      const updatePromises = settingsToReset.map(setting =>
        this.prisma.setting.upsert({
          where: { key: setting.key },
          update: {
            value: setting.value,
            description: setting.description,
            category: setting.category,
            isPublic: setting.isPublic,
          },
          create: setting,
        })
      );

      await Promise.all(updatePromises);

      logger.info('系统设置重置成功', {
        category,
        count: settingsToReset.length,
        userId: req.user?.id,
      });

      res.json(ApiResponse.success(null, '设置重置成功'));
    } catch (error) {
      logger.error('重置系统设置失败', { error, category });
      throw error;
    }
  }

  /**
   * 获取默认设置
   */
  private getDefaultSettings() {
    return [
      // 网站基本设置
      {
        key: 'site_name',
        value: 'POS机产品展示网站',
        description: '网站名称',
        category: 'site',
        isPublic: true,
      },
      {
        key: 'site_description',
        value: '专业的POS机产品展示平台，提供最新的支付终端设备信息',
        description: '网站描述',
        category: 'site',
        isPublic: true,
      },
      {
        key: 'site_keywords',
        value: 'POS机,支付终端,移动支付,智能POS,收银设备',
        description: '网站关键词',
        category: 'site',
        isPublic: true,
      },
      {
        key: 'site_logo',
        value: '/images/logo.png',
        description: '网站Logo',
        category: 'site',
        isPublic: true,
      },
      {
        key: 'site_favicon',
        value: '/favicon.ico',
        description: '网站图标',
        category: 'site',
        isPublic: true,
      },

      // 联系信息
      {
        key: 'contact_phone',
        value: '************',
        description: '联系电话',
        category: 'contact',
        isPublic: true,
      },
      {
        key: 'contact_email',
        value: '<EMAIL>',
        description: '联系邮箱',
        category: 'contact',
        isPublic: true,
      },
      {
        key: 'contact_address',
        value: '北京市朝阳区科技园区创新大厦A座1001室',
        description: '联系地址',
        category: 'contact',
        isPublic: true,
      },
      {
        key: 'contact_working_hours',
        value: '周一至周五 9:00-18:00',
        description: '工作时间',
        category: 'contact',
        isPublic: true,
      },

      // SEO设置
      {
        key: 'seo_title_suffix',
        value: ' - POS机产品展示网站',
        description: 'SEO标题后缀',
        category: 'seo',
        isPublic: false,
      },
      {
        key: 'seo_robots',
        value: 'index,follow',
        description: 'robots设置',
        category: 'seo',
        isPublic: true,
      },

      // 分析统计
      {
        key: 'analytics_google_id',
        value: '',
        description: 'Google Analytics ID',
        category: 'analytics',
        isPublic: true,
      },
      {
        key: 'analytics_baidu_id',
        value: '',
        description: '百度统计ID',
        category: 'analytics',
        isPublic: true,
      },

      // 系统设置
      {
        key: 'system_maintenance_mode',
        value: 'false',
        description: '维护模式',
        category: 'system',
        isPublic: false,
      },
      {
        key: 'system_registration_enabled',
        value: 'true',
        description: '允许用户注册',
        category: 'system',
        isPublic: false,
      },
      {
        key: 'system_comment_enabled',
        value: 'true',
        description: '允许评论',
        category: 'system',
        isPublic: false,
      },
    ];
  }
}
