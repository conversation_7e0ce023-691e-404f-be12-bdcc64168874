/**
 * 日志工具
 */

import winston from 'winston';
import path from 'path';
import fs from 'fs';
import { config } from '../config';

// 确保日志目录存在
if (!fs.existsSync(config.logging.path)) {
  fs.mkdirSync(config.logging.path, { recursive: true });
}

/**
 * 自定义日志格式
 */
const logFormat = winston.format.combine(
  winston.format.timestamp({ format: 'YYYY-MM-DD HH:mm:ss' }),
  winston.format.errors({ stack: true }),
  winston.format.printf(({ timestamp, level, message, stack, ...meta }) => {
    let log = `${timestamp} [${level.toUpperCase()}]: ${message}`;
    
    // 添加堆栈信息
    if (stack) {
      log += `\n${stack}`;
    }
    
    // 添加元数据
    if (Object.keys(meta).length > 0) {
      log += `\n${JSON.stringify(meta, null, 2)}`;
    }
    
    return log;
  })
);

/**
 * 控制台格式
 */
const consoleFormat = winston.format.combine(
  winston.format.colorize(),
  winston.format.timestamp({ format: 'HH:mm:ss' }),
  winston.format.printf(({ timestamp, level, message, stack }) => {
    let log = `${timestamp} ${level}: ${message}`;
    if (stack) {
      log += `\n${stack}`;
    }
    return log;
  })
);

/**
 * 创建Winston日志器
 */
export const logger = winston.createLogger({
  level: config.logging.level,
  format: logFormat,
  defaultMeta: {
    service: 'pos-website-backend',
    environment: config.env,
  },
  transports: [
    // 控制台输出
    new winston.transports.Console({
      format: consoleFormat,
      silent: config.env === 'test',
    }),
    
    // 错误日志文件
    new winston.transports.File({
      filename: path.join(config.logging.path, 'error.log'),
      level: 'error',
      maxsize: 10 * 1024 * 1024, // 10MB
      maxFiles: 5,
      tailable: true,
    }),
    
    // 警告日志文件
    new winston.transports.File({
      filename: path.join(config.logging.path, 'warn.log'),
      level: 'warn',
      maxsize: 10 * 1024 * 1024, // 10MB
      maxFiles: 3,
      tailable: true,
    }),
    
    // 所有日志文件
    new winston.transports.File({
      filename: path.join(config.logging.path, 'combined.log'),
      maxsize: 20 * 1024 * 1024, // 20MB
      maxFiles: 10,
      tailable: true,
    }),
  ],
  
  // 异常处理
  exceptionHandlers: [
    new winston.transports.File({
      filename: path.join(config.logging.path, 'exceptions.log'),
      maxsize: 10 * 1024 * 1024, // 10MB
      maxFiles: 3,
    }),
  ],
  
  // 拒绝处理
  rejectionHandlers: [
    new winston.transports.File({
      filename: path.join(config.logging.path, 'rejections.log'),
      maxsize: 10 * 1024 * 1024, // 10MB
      maxFiles: 3,
    }),
  ],
  
  exitOnError: false,
});

/**
 * 日志级别枚举
 */
export enum LogLevel {
  ERROR = 'error',
  WARN = 'warn',
  INFO = 'info',
  DEBUG = 'debug',
}

/**
 * 结构化日志接口
 */
export interface LogContext {
  userId?: number;
  requestId?: string;
  ip?: string;
  userAgent?: string;
  method?: string;
  url?: string;
  statusCode?: number;
  responseTime?: number;
  error?: Error;
  [key: string]: any;
}

/**
 * 扩展的日志器类
 */
export class Logger {
  private winston: winston.Logger;

  constructor(winston: winston.Logger) {
    this.winston = winston;
  }

  /**
   * 错误日志
   */
  error(message: string, context?: LogContext): void {
    this.winston.error(message, context);
  }

  /**
   * 警告日志
   */
  warn(message: string, context?: LogContext): void {
    this.winston.warn(message, context);
  }

  /**
   * 信息日志
   */
  info(message: string, context?: LogContext): void {
    this.winston.info(message, context);
  }

  /**
   * 调试日志
   */
  debug(message: string, context?: LogContext): void {
    this.winston.debug(message, context);
  }

  /**
   * HTTP请求日志
   */
  http(message: string, context: LogContext): void {
    this.winston.info(`[HTTP] ${message}`, context);
  }

  /**
   * 数据库操作日志
   */
  database(message: string, context?: LogContext): void {
    this.winston.info(`[DB] ${message}`, context);
  }

  /**
   * 认证日志
   */
  auth(message: string, context?: LogContext): void {
    this.winston.info(`[AUTH] ${message}`, context);
  }

  /**
   * 业务逻辑日志
   */
  business(message: string, context?: LogContext): void {
    this.winston.info(`[BUSINESS] ${message}`, context);
  }

  /**
   * 性能日志
   */
  performance(message: string, context?: LogContext): void {
    this.winston.info(`[PERF] ${message}`, context);
  }

  /**
   * 安全日志
   */
  security(message: string, context?: LogContext): void {
    this.winston.warn(`[SECURITY] ${message}`, context);
  }
}

// 创建扩展日志器实例
export const extendedLogger = new Logger(logger);

/**
 * 请求日志中间件辅助函数
 */
export function createRequestLogger() {
  return (req: any, res: any, next: any) => {
    const startTime = Date.now();
    const requestId = Math.random().toString(36).substring(7);
    
    // 添加请求ID到请求对象
    req.requestId = requestId;
    
    // 记录请求开始
    logger.info('请求开始', {
      requestId,
      method: req.method,
      url: req.url,
      ip: req.ip,
      userAgent: req.get('User-Agent'),
      userId: req.user?.id,
    });
    
    // 监听响应结束
    res.on('finish', () => {
      const responseTime = Date.now() - startTime;
      
      logger.info('请求完成', {
        requestId,
        method: req.method,
        url: req.url,
        statusCode: res.statusCode,
        responseTime,
        ip: req.ip,
        userId: req.user?.id,
      });
    });
    
    next();
  };
}

/**
 * 错误日志辅助函数
 */
export function logError(error: Error, context?: LogContext): void {
  logger.error(error.message, {
    ...context,
    stack: error.stack,
    name: error.name,
  });
}

/**
 * 性能监控辅助函数
 */
export function logPerformance(operation: string, duration: number, context?: LogContext): void {
  const level = duration > 1000 ? 'warn' : 'info';
  logger[level](`性能监控: ${operation}`, {
    ...context,
    duration,
    slow: duration > 1000,
  });
}

/**
 * 清理旧日志文件
 */
export async function cleanupLogs(daysToKeep: number = 30): Promise<void> {
  try {
    const logDir = config.logging.path;
    const files = fs.readdirSync(logDir);
    const cutoffDate = new Date();
    cutoffDate.setDate(cutoffDate.getDate() - daysToKeep);
    
    for (const file of files) {
      const filePath = path.join(logDir, file);
      const stats = fs.statSync(filePath);
      
      if (stats.mtime < cutoffDate) {
        fs.unlinkSync(filePath);
        logger.info(`已删除旧日志文件: ${file}`);
      }
    }
  } catch (error) {
    logger.error('清理日志文件失败:', error);
  }
}

// 定期清理日志文件（每天执行一次）
if (config.env === 'production') {
  setInterval(() => {
    cleanupLogs(30); // 保留30天的日志
  }, 24 * 60 * 60 * 1000); // 24小时
}

export default logger;
