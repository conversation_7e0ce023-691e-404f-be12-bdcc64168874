/**
 * 主路由文件
 */

import { Router } from 'express';
import authRoutes from './auth.routes';
import productRoutes from './product.routes';
import categoryRoutes from './category.routes';
import articleRoutes from './article.routes';
import companyRoutes from './company.routes';
import contactRoutes from './contact.routes';
import uploadRoutes from './upload.routes';
import analyticsRoutes from './analytics.routes';
import settingRoutes from './setting.routes';

const router = Router();

/**
 * API路由配置
 */
router.use('/auth', authRoutes);
router.use('/products', productRoutes);
router.use('/categories', categoryRoutes);
router.use('/articles', articleRoutes);
router.use('/company', companyRoutes);
router.use('/contact', contactRoutes);
router.use('/upload', uploadRoutes);
router.use('/analytics', analyticsRoutes);
router.use('/settings', settingRoutes);

/**
 * API健康检查
 */
router.get('/health', (req, res) => {
  res.json({
    success: true,
    message: 'API服务运行正常',
    timestamp: new Date().toISOString(),
    version: process.env.npm_package_version || '1.0.0',
  });
});

/**
 * API信息
 */
router.get('/info', (req, res) => {
  res.json({
    success: true,
    data: {
      name: 'POS机产品展示网站 API',
      version: process.env.npm_package_version || '1.0.0',
      description: '专业的POS机产品展示平台API服务',
      author: 'POS机产品展示网站',
      environment: process.env.NODE_ENV || 'development',
      timestamp: new Date().toISOString(),
    },
  });
});

export default router;
