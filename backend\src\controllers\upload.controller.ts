/**
 * 文件上传控制器
 */

import { Request, Response } from 'express';
import path from 'path';
import sharp from 'sharp';
import { logger } from '../utils/logger';
import { ApiResponse } from '../utils/response';
import { ValidationError } from '../middleware/errorHandler';
import { config } from '../config';
import { getFileUrl } from '../middleware/upload';

/**
 * 文件上传控制器
 */
export class UploadController {
  /**
   * 上传图片
   */
  public async uploadImage(req: Request, res: Response): Promise<void> {
    try {
      if (!req.file) {
        throw new ValidationError('请选择要上传的图片');
      }

      const file = req.file;
      
      // 验证文件类型
      if (!config.upload.allowedImageTypes.includes(file.mimetype)) {
        throw new ValidationError('不支持的图片格式');
      }

      // 验证文件大小
      if (file.size > config.upload.maxImageSize) {
        throw new ValidationError('图片文件过大');
      }

      // 处理图片（压缩、生成缩略图等）
      const processedImages = await this.processImage(file);

      const result = {
        filename: file.filename,
        originalName: file.originalname,
        mimetype: file.mimetype,
        size: file.size,
        url: getFileUrl(file.filename, true),
        processed: processedImages,
      };

      logger.info('图片上传成功', {
        filename: file.filename,
        originalName: file.originalname,
        size: file.size,
        userId: req.user?.id,
      });

      res.json(ApiResponse.success(result, '图片上传成功'));
    } catch (error) {
      logger.error('图片上传失败', { error });
      throw error;
    }
  }

  /**
   * 上传文件
   */
  public async uploadFile(req: Request, res: Response): Promise<void> {
    try {
      if (!req.file) {
        throw new ValidationError('请选择要上传的文件');
      }

      const file = req.file;
      
      // 验证文件类型
      if (!config.upload.allowedFileTypes.includes(file.mimetype)) {
        throw new ValidationError('不支持的文件格式');
      }

      // 验证文件大小
      if (file.size > config.upload.maxFileSize) {
        throw new ValidationError('文件过大');
      }

      const result = {
        filename: file.filename,
        originalName: file.originalname,
        mimetype: file.mimetype,
        size: file.size,
        url: getFileUrl(file.filename, false),
      };

      logger.info('文件上传成功', {
        filename: file.filename,
        originalName: file.originalname,
        size: file.size,
        userId: req.user?.id,
      });

      res.json(ApiResponse.success(result, '文件上传成功'));
    } catch (error) {
      logger.error('文件上传失败', { error });
      throw error;
    }
  }

  /**
   * 批量上传图片
   */
  public async uploadMultiple(req: Request, res: Response): Promise<void> {
    try {
      const files = req.files as Express.Multer.File[];
      
      if (!files || files.length === 0) {
        throw new ValidationError('请选择要上传的图片');
      }

      const results = [];

      for (const file of files) {
        // 验证文件类型
        if (!config.upload.allowedImageTypes.includes(file.mimetype)) {
          logger.warn('跳过不支持的文件格式', { 
            filename: file.originalname,
            mimetype: file.mimetype 
          });
          continue;
        }

        // 验证文件大小
        if (file.size > config.upload.maxImageSize) {
          logger.warn('跳过过大的文件', { 
            filename: file.originalname,
            size: file.size 
          });
          continue;
        }

        try {
          // 处理图片
          const processedImages = await this.processImage(file);

          const result = {
            filename: file.filename,
            originalName: file.originalname,
            mimetype: file.mimetype,
            size: file.size,
            url: getFileUrl(file.filename, true),
            processed: processedImages,
          };

          results.push(result);
        } catch (error) {
          logger.error('处理图片失败', { 
            error, 
            filename: file.originalname 
          });
        }
      }

      logger.info('批量图片上传完成', {
        totalFiles: files.length,
        successCount: results.length,
        userId: req.user?.id,
      });

      res.json(ApiResponse.success(results, `成功上传 ${results.length} 张图片`));
    } catch (error) {
      logger.error('批量图片上传失败', { error });
      throw error;
    }
  }

  /**
   * 处理图片（压缩、生成缩略图）
   */
  private async processImage(file: Express.Multer.File): Promise<{
    thumbnail?: string;
    medium?: string;
    large?: string;
  }> {
    const processed: any = {};

    try {
      const inputPath = file.path;
      const ext = path.extname(file.filename);
      const baseName = path.basename(file.filename, ext);
      const outputDir = path.dirname(inputPath);

      // 生成缩略图
      const thumbnailFilename = `${baseName}_thumb${ext}`;
      const thumbnailPath = path.join(outputDir, thumbnailFilename);
      
      await sharp(inputPath)
        .resize(config.upload.thumbnailSize.width, config.upload.thumbnailSize.height, {
          fit: 'cover',
          position: 'center',
        })
        .jpeg({ quality: config.upload.imageQuality })
        .toFile(thumbnailPath);
      
      processed.thumbnail = {
        filename: thumbnailFilename,
        url: getFileUrl(thumbnailFilename, true),
        width: config.upload.thumbnailSize.width,
        height: config.upload.thumbnailSize.height,
      };

      // 生成中等尺寸图片
      const mediumFilename = `${baseName}_medium${ext}`;
      const mediumPath = path.join(outputDir, mediumFilename);
      
      await sharp(inputPath)
        .resize(config.upload.mediumSize.width, config.upload.mediumSize.height, {
          fit: 'inside',
          withoutEnlargement: true,
        })
        .jpeg({ quality: config.upload.imageQuality })
        .toFile(mediumPath);
      
      processed.medium = {
        filename: mediumFilename,
        url: getFileUrl(mediumFilename, true),
        width: config.upload.mediumSize.width,
        height: config.upload.mediumSize.height,
      };

      // 生成大尺寸图片
      const largeFilename = `${baseName}_large${ext}`;
      const largePath = path.join(outputDir, largeFilename);
      
      await sharp(inputPath)
        .resize(config.upload.largeSize.width, config.upload.largeSize.height, {
          fit: 'inside',
          withoutEnlargement: true,
        })
        .jpeg({ quality: config.upload.imageQuality })
        .toFile(largePath);
      
      processed.large = {
        filename: largeFilename,
        url: getFileUrl(largeFilename, true),
        width: config.upload.largeSize.width,
        height: config.upload.largeSize.height,
      };

    } catch (error) {
      logger.error('图片处理失败', { error, filename: file.filename });
    }

    return processed;
  }

  /**
   * 删除文件
   */
  public async deleteFile(req: Request, res: Response): Promise<void> {
    const { filename } = req.params;
    const { type = 'image' } = req.query;

    try {
      const isImage = type === 'image';
      
      // 这里可以添加权限检查，确保用户只能删除自己上传的文件
      
      const { deleteFile } = await import('../middleware/upload');
      await deleteFile(filename, isImage);

      // 如果是图片，还需要删除处理后的版本
      if (isImage) {
        const ext = path.extname(filename);
        const baseName = path.basename(filename, ext);
        
        const variants = ['_thumb', '_medium', '_large'];
        for (const variant of variants) {
          const variantFilename = `${baseName}${variant}${ext}`;
          try {
            await deleteFile(variantFilename, true);
          } catch {
            // 忽略删除失败的情况
          }
        }
      }

      logger.info('文件删除成功', {
        filename,
        type,
        userId: req.user?.id,
      });

      res.json(ApiResponse.success(null, '文件删除成功'));
    } catch (error) {
      logger.error('文件删除失败', { error, filename });
      throw error;
    }
  }

  /**
   * 获取文件信息
   */
  public async getFileInfo(req: Request, res: Response): Promise<void> {
    const { filename } = req.params;
    const { type = 'image' } = req.query;

    try {
      const isImage = type === 'image';
      
      const { getFileInfo } = await import('../middleware/upload');
      const fileInfo = getFileInfo(filename, isImage);

      if (!fileInfo || !fileInfo.exists) {
        throw new ValidationError('文件不存在');
      }

      const result = {
        filename,
        exists: fileInfo.exists,
        size: fileInfo.size,
        url: getFileUrl(filename, isImage),
      };

      res.json(ApiResponse.success(result));
    } catch (error) {
      logger.error('获取文件信息失败', { error, filename });
      throw error;
    }
  }
}
