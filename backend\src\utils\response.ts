/**
 * API响应工具
 */

import { IApiResponse, IPaginationResponse } from '@/shared/types';

/**
 * API响应工具类
 */
export class ApiResponse {
  /**
   * 成功响应
   */
  static success<T>(data: T, message?: string): IApiResponse<T> {
    return {
      success: true,
      data,
      message: message || '操作成功',
    };
  }

  /**
   * 错误响应
   */
  static error(message: string, errors?: string[]): IApiResponse<null> {
    return {
      success: false,
      data: null,
      message,
      errors,
    };
  }

  /**
   * 分页响应
   */
  static paginated<T>(
    data: T[],
    pagination: {
      page: number;
      limit: number;
      total: number;
      totalPages: number;
      hasNext: boolean;
      hasPrev: boolean;
    },
    message?: string
  ): IApiResponse<IPaginationResponse<T>> {
    return {
      success: true,
      data: {
        data,
        pagination,
      },
      message: message || '获取数据成功',
    };
  }

  /**
   * 创建响应
   */
  static created<T>(data: T, message?: string): IApiResponse<T> {
    return {
      success: true,
      data,
      message: message || '创建成功',
    };
  }

  /**
   * 更新响应
   */
  static updated<T>(data: T, message?: string): IApiResponse<T> {
    return {
      success: true,
      data,
      message: message || '更新成功',
    };
  }

  /**
   * 删除响应
   */
  static deleted(message?: string): IApiResponse<null> {
    return {
      success: true,
      data: null,
      message: message || '删除成功',
    };
  }

  /**
   * 无内容响应
   */
  static noContent(message?: string): IApiResponse<null> {
    return {
      success: true,
      data: null,
      message: message || '操作成功',
    };
  }
}

/**
 * 分页工具类
 */
export class PaginationHelper {
  /**
   * 计算分页信息
   */
  static calculate(page: number, limit: number, total: number) {
    const totalPages = Math.ceil(total / limit);
    const hasNext = page < totalPages;
    const hasPrev = page > 1;

    return {
      page,
      limit,
      total,
      totalPages,
      hasNext,
      hasPrev,
    };
  }

  /**
   * 计算跳过的记录数
   */
  static getSkip(page: number, limit: number): number {
    return (page - 1) * limit;
  }

  /**
   * 验证分页参数
   */
  static validateParams(page: number, limit: number): { page: number; limit: number } {
    const validatedPage = Math.max(1, Math.floor(page) || 1);
    const validatedLimit = Math.min(100, Math.max(1, Math.floor(limit) || 10));

    return {
      page: validatedPage,
      limit: validatedLimit,
    };
  }
}

/**
 * 响应状态码常量
 */
export const HTTP_STATUS = {
  OK: 200,
  CREATED: 201,
  NO_CONTENT: 204,
  BAD_REQUEST: 400,
  UNAUTHORIZED: 401,
  FORBIDDEN: 403,
  NOT_FOUND: 404,
  CONFLICT: 409,
  UNPROCESSABLE_ENTITY: 422,
  TOO_MANY_REQUESTS: 429,
  INTERNAL_SERVER_ERROR: 500,
} as const;

/**
 * 响应消息常量
 */
export const RESPONSE_MESSAGES = {
  SUCCESS: '操作成功',
  CREATED: '创建成功',
  UPDATED: '更新成功',
  DELETED: '删除成功',
  NOT_FOUND: '资源未找到',
  UNAUTHORIZED: '未授权访问',
  FORBIDDEN: '禁止访问',
  VALIDATION_ERROR: '数据验证失败',
  INTERNAL_ERROR: '服务器内部错误',
} as const;
