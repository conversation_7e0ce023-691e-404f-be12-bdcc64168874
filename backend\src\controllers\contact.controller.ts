/**
 * 联系咨询控制器
 */

import { Request, Response } from 'express';
import { DatabaseManager } from '../config/database';
import { logger } from '../utils/logger';
import { ApiResponse, PaginationHelper } from '../utils/response';
import { NotFoundError, ValidationError } from '../middleware/errorHandler';
import { EmailService } from '../services/email.service';
import { InquiryStatus } from '@prisma/client';

/**
 * 联系咨询控制器
 */
export class ContactController {
  private prisma = DatabaseManager.getInstance().getPrisma();
  private emailService = new EmailService();

  /**
   * 提交联系咨询
   */
  public async submitInquiry(req: Request, res: Response): Promise<void> {
    const { name, email, phone, company, subject, message } = req.body;

    try {
      // 创建咨询记录
      const inquiry = await this.prisma.contactInquiry.create({
        data: {
          name,
          email,
          phone,
          company,
          subject,
          message,
          status: InquiryStatus.PENDING,
          userId: req.user?.id, // 如果用户已登录，关联用户ID
        },
      });

      // 发送确认邮件给用户
      try {
        await this.emailService.sendContactConfirmationEmail(email, name, subject);
      } catch (emailError) {
        logger.warn('发送确认邮件失败', { 
          error: emailError, 
          inquiryId: inquiry.id 
        });
      }

      // 发送通知邮件给管理员
      try {
        await this.emailService.sendNewInquiryNotification(inquiry);
      } catch (emailError) {
        logger.warn('发送管理员通知邮件失败', { 
          error: emailError, 
          inquiryId: inquiry.id 
        });
      }

      logger.info('联系咨询提交成功', {
        inquiryId: inquiry.id,
        name,
        email,
        subject,
        ip: req.ip,
      });

      res.status(201).json(ApiResponse.created(
        { id: inquiry.id },
        '咨询提交成功，我们会尽快回复您'
      ));
    } catch (error) {
      logger.error('提交联系咨询失败', { error, data: req.body });
      throw error;
    }
  }

  /**
   * 获取咨询列表（管理员）
   */
  public async getInquiries(req: Request, res: Response): Promise<void> {
    const { page = 1, limit = 20, status, search } = req.query;

    // 验证和处理分页参数
    const { page: validPage, limit: validLimit } = PaginationHelper.validateParams(
      Number(page),
      Number(limit)
    );

    // 构建查询条件
    const where: any = {};

    if (status && typeof status === 'string') {
      where.status = status as InquiryStatus;
    }

    if (search && typeof search === 'string') {
      where.OR = [
        { name: { contains: search } },
        { email: { contains: search } },
        { company: { contains: search } },
        { subject: { contains: search } },
        { message: { contains: search } },
      ];
    }

    try {
      // 获取总数
      const total = await this.prisma.contactInquiry.count({ where });

      // 获取咨询列表
      const inquiries = await this.prisma.contactInquiry.findMany({
        where,
        include: {
          user: {
            select: {
              id: true,
              name: true,
              email: true,
            },
          },
          assignee: {
            select: {
              id: true,
              name: true,
            },
          },
        },
        orderBy: { createdAt: 'desc' },
        skip: PaginationHelper.getSkip(validPage, validLimit),
        take: validLimit,
      });

      // 计算分页信息
      const pagination = PaginationHelper.calculate(validPage, validLimit, total);

      res.json(ApiResponse.paginated(inquiries, pagination));
    } catch (error) {
      logger.error('获取咨询列表失败', { error });
      throw error;
    }
  }

  /**
   * 获取咨询详情（管理员）
   */
  public async getInquiry(req: Request, res: Response): Promise<void> {
    const { id } = req.params;

    try {
      const inquiryId = parseInt(id);
      
      const inquiry = await this.prisma.contactInquiry.findUnique({
        where: { id: inquiryId },
        include: {
          user: {
            select: {
              id: true,
              name: true,
              email: true,
              avatar: true,
            },
          },
          assignee: {
            select: {
              id: true,
              name: true,
              email: true,
            },
          },
        },
      });

      if (!inquiry) {
        throw new NotFoundError('咨询记录');
      }

      res.json(ApiResponse.success(inquiry));
    } catch (error) {
      logger.error('获取咨询详情失败', { error, id });
      throw error;
    }
  }

  /**
   * 回复咨询（管理员）
   */
  public async replyInquiry(req: Request, res: Response): Promise<void> {
    const { id } = req.params;
    const { reply } = req.body;

    try {
      const inquiryId = parseInt(id);
      
      const inquiry = await this.prisma.contactInquiry.findUnique({
        where: { id: inquiryId },
      });

      if (!inquiry) {
        throw new NotFoundError('咨询记录');
      }

      // 更新咨询状态和回复信息
      const updatedInquiry = await this.prisma.contactInquiry.update({
        where: { id: inquiryId },
        data: {
          status: InquiryStatus.RESOLVED,
          assignedTo: req.user!.id,
          repliedAt: new Date(),
        },
        include: {
          assignee: {
            select: {
              id: true,
              name: true,
            },
          },
        },
      });

      // 发送回复邮件
      try {
        await this.emailService.sendContactReplyEmail(
          inquiry.email,
          inquiry.name,
          inquiry.message,
          reply
        );
      } catch (emailError) {
        logger.warn('发送回复邮件失败', { 
          error: emailError, 
          inquiryId 
        });
      }

      logger.info('咨询回复成功', {
        inquiryId,
        userId: req.user?.id,
        replyLength: reply.length,
      });

      res.json(ApiResponse.success(updatedInquiry, '回复发送成功'));
    } catch (error) {
      logger.error('回复咨询失败', { error, id });
      throw error;
    }
  }

  /**
   * 更新咨询状态（管理员）
   */
  public async updateInquiryStatus(req: Request, res: Response): Promise<void> {
    const { id } = req.params;
    const { status } = req.body;

    try {
      const inquiryId = parseInt(id);
      
      const inquiry = await this.prisma.contactInquiry.findUnique({
        where: { id: inquiryId },
      });

      if (!inquiry) {
        throw new NotFoundError('咨询记录');
      }

      const updatedInquiry = await this.prisma.contactInquiry.update({
        where: { id: inquiryId },
        data: {
          status: status as InquiryStatus,
          ...(status === InquiryStatus.IN_PROGRESS && {
            assignedTo: req.user!.id,
          }),
        },
        include: {
          assignee: {
            select: {
              id: true,
              name: true,
            },
          },
        },
      });

      logger.info('咨询状态更新成功', {
        inquiryId,
        oldStatus: inquiry.status,
        newStatus: status,
        userId: req.user?.id,
      });

      res.json(ApiResponse.success(updatedInquiry, '状态更新成功'));
    } catch (error) {
      logger.error('更新咨询状态失败', { error, id });
      throw error;
    }
  }

  /**
   * 分配咨询（管理员）
   */
  public async assignInquiry(req: Request, res: Response): Promise<void> {
    const { id } = req.params;
    const { assigneeId } = req.body;

    try {
      const inquiryId = parseInt(id);
      
      const inquiry = await this.prisma.contactInquiry.findUnique({
        where: { id: inquiryId },
      });

      if (!inquiry) {
        throw new NotFoundError('咨询记录');
      }

      // 检查被分配的用户是否存在
      if (assigneeId) {
        const assignee = await this.prisma.user.findUnique({
          where: { id: assigneeId },
        });

        if (!assignee) {
          throw new NotFoundError('指定的用户');
        }
      }

      const updatedInquiry = await this.prisma.contactInquiry.update({
        where: { id: inquiryId },
        data: {
          assignedTo: assigneeId || null,
          status: assigneeId ? InquiryStatus.IN_PROGRESS : InquiryStatus.PENDING,
        },
        include: {
          assignee: {
            select: {
              id: true,
              name: true,
              email: true,
            },
          },
        },
      });

      logger.info('咨询分配成功', {
        inquiryId,
        assigneeId,
        userId: req.user?.id,
      });

      res.json(ApiResponse.success(updatedInquiry, '分配成功'));
    } catch (error) {
      logger.error('分配咨询失败', { error, id });
      throw error;
    }
  }

  /**
   * 获取咨询统计信息（管理员）
   */
  public async getInquiryStats(req: Request, res: Response): Promise<void> {
    try {
      // 获取各状态的咨询数量
      const statusStats = await this.prisma.contactInquiry.groupBy({
        by: ['status'],
        _count: {
          status: true,
        },
      });

      // 获取最近7天的咨询数量
      const sevenDaysAgo = new Date();
      sevenDaysAgo.setDate(sevenDaysAgo.getDate() - 7);

      const recentStats = await this.prisma.contactInquiry.findMany({
        where: {
          createdAt: {
            gte: sevenDaysAgo,
          },
        },
        select: {
          createdAt: true,
        },
      });

      // 按日期分组统计
      const dailyStats = recentStats.reduce((acc, inquiry) => {
        const date = inquiry.createdAt.toISOString().split('T')[0];
        acc[date] = (acc[date] || 0) + 1;
        return acc;
      }, {} as Record<string, number>);

      // 获取总数
      const totalCount = await this.prisma.contactInquiry.count();

      // 获取今日新增
      const today = new Date();
      today.setHours(0, 0, 0, 0);
      const todayCount = await this.prisma.contactInquiry.count({
        where: {
          createdAt: {
            gte: today,
          },
        },
      });

      const stats = {
        total: totalCount,
        today: todayCount,
        byStatus: statusStats.reduce((acc, stat) => {
          acc[stat.status] = stat._count.status;
          return acc;
        }, {} as Record<string, number>),
        daily: dailyStats,
      };

      res.json(ApiResponse.success(stats));
    } catch (error) {
      logger.error('获取咨询统计信息失败', { error });
      throw error;
    }
  }
}
