{"name": "pos-website-backend", "version": "1.0.0", "description": "POS机产品展示网站后端API服务", "main": "dist/index.js", "scripts": {"dev": "tsx watch src/index.ts", "build": "tsc", "start": "node dist/index.js", "test": "vitest", "test:coverage": "vitest --coverage", "lint": "eslint src --ext .ts", "lint:fix": "eslint src --ext .ts --fix", "prisma:generate": "prisma generate", "prisma:migrate": "prisma migrate dev", "prisma:deploy": "prisma migrate deploy", "prisma:seed": "tsx prisma/seed.ts", "prisma:studio": "prisma studio", "prisma:reset": "prisma migrate reset"}, "keywords": ["pos", "website", "api", "express", "typescript", "prisma"], "author": "Your Name", "license": "MIT", "dependencies": {"@prisma/client": "^5.7.1", "express": "^4.18.2", "cors": "^2.8.5", "helmet": "^7.1.0", "compression": "^1.7.4", "morgan": "^1.10.0", "winston": "^3.11.0", "bcrypt": "^5.1.1", "jsonwebtoken": "^9.0.2", "joi": "^17.11.0", "multer": "^1.4.5-lts.1", "sharp": "^0.33.1", "nodemailer": "^6.9.7", "express-rate-limit": "^7.1.5", "express-validator": "^7.0.1", "cookie-parser": "^1.4.6", "dotenv": "^16.3.1", "mysql2": "^3.6.5", "redis": "^4.6.11", "swagger-jsdoc": "^6.2.8", "swagger-ui-express": "^5.0.0"}, "devDependencies": {"@types/express": "^4.17.21", "@types/cors": "^2.8.17", "@types/compression": "^1.7.5", "@types/morgan": "^1.9.9", "@types/bcrypt": "^5.0.2", "@types/jsonwebtoken": "^9.0.5", "@types/multer": "^1.4.11", "@types/nodemailer": "^6.4.14", "@types/cookie-parser": "^1.4.6", "@types/swagger-jsdoc": "^6.0.4", "@types/swagger-ui-express": "^4.1.6", "@types/node": "^20.10.4", "@typescript-eslint/eslint-plugin": "^6.13.2", "@typescript-eslint/parser": "^6.13.2", "eslint": "^8.55.0", "prisma": "^5.7.1", "tsx": "^4.6.2", "typescript": "^5.3.3", "vitest": "^1.0.4", "@vitest/coverage-v8": "^1.0.4"}, "engines": {"node": ">=18.0.0"}}