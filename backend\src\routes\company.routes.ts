/**
 * 公司信息路由
 */

import { Router } from 'express';
import { body } from 'express-validator';
import { CompanyController } from '../controllers/company.controller';
import { authMiddleware, requireEditor } from '../middleware/auth';
import { asyncHandler } from '../middleware/errorHandler';
import { validateRequest } from '../middleware/validation';

const router = Router();
const companyController = new CompanyController();

/**
 * 公司信息更新验证规则
 */
const updateCompanyValidation = [
  body('name')
    .optional()
    .trim()
    .isLength({ min: 2, max: 255 })
    .withMessage('公司名称长度必须在2-255个字符之间'),
  body('description')
    .optional()
    .isLength({ max: 5000 })
    .withMessage('公司描述不能超过5000个字符'),
  body('address')
    .optional()
    .isLength({ max: 500 })
    .withMessage('公司地址不能超过500个字符'),
  body('phone')
    .optional()
    .isLength({ max: 50 })
    .withMessage('联系电话不能超过50个字符'),
  body('email')
    .optional()
    .isEmail()
    .withMessage('请输入有效的邮箱地址'),
  body('website')
    .optional()
    .isURL()
    .withMessage('请输入有效的网站地址'),
  body('founded')
    .optional()
    .isInt({ min: 1800, max: new Date().getFullYear() })
    .withMessage('成立年份必须在1800年到当前年份之间'),
  body('employees')
    .optional()
    .isLength({ max: 50 })
    .withMessage('员工规模不能超过50个字符'),
  body('revenue')
    .optional()
    .isLength({ max: 50 })
    .withMessage('营收规模不能超过50个字符'),
  body('mission')
    .optional()
    .isLength({ max: 2000 })
    .withMessage('公司使命不能超过2000个字符'),
  body('vision')
    .optional()
    .isLength({ max: 2000 })
    .withMessage('公司愿景不能超过2000个字符'),
  body('values')
    .optional()
    .isLength({ max: 2000 })
    .withMessage('公司价值观不能超过2000个字符'),
];

/**
 * @swagger
 * /api/company/info:
 *   get:
 *     summary: 获取公司信息
 *     tags: [公司信息]
 *     responses:
 *       200:
 *         description: 获取成功
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                 data:
 *                   $ref: '#/components/schemas/CompanyInfo'
 */
router.get('/info',
  asyncHandler(companyController.getCompanyInfo.bind(companyController))
);

/**
 * @swagger
 * /api/company/info:
 *   put:
 *     summary: 更新公司信息
 *     tags: [公司信息]
 *     security:
 *       - bearerAuth: []
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             $ref: '#/components/schemas/UpdateCompanyInfoDto'
 *     responses:
 *       200:
 *         description: 更新成功
 *       400:
 *         description: 请求参数错误
 *       401:
 *         description: 未授权
 *       403:
 *         description: 权限不足
 */
router.put('/info',
  authMiddleware,
  requireEditor,
  updateCompanyValidation,
  validateRequest,
  asyncHandler(companyController.updateCompanyInfo.bind(companyController))
);

export default router;
