/**
 * 错误处理中间件
 */

import { Request, Response, NextFunction } from 'express';
import { logger } from '../utils/logger';
import { config } from '../config';

/**
 * 应用错误基类
 */
export class AppError extends Error {
  public statusCode: number;
  public isOperational: boolean;
  public code?: string;

  constructor(message: string, statusCode: number = 500, code?: string) {
    super(message);
    this.statusCode = statusCode;
    this.isOperational = true;
    this.code = code;

    Error.captureStackTrace(this, this.constructor);
  }
}

/**
 * 业务错误类型
 */
export class ValidationError extends AppError {
  constructor(message: string, code?: string) {
    super(message, 400, code || 'VALIDATION_ERROR');
  }
}

export class NotFoundError extends AppError {
  constructor(resource: string) {
    super(`${resource} 未找到`, 404, 'NOT_FOUND');
  }
}

export class UnauthorizedError extends AppError {
  constructor(message: string = '未授权访问') {
    super(message, 401, 'UNAUTHORIZED');
  }
}

export class ForbiddenError extends AppError {
  constructor(message: string = '禁止访问') {
    super(message, 403, 'FORBIDDEN');
  }
}

export class ConflictError extends AppError {
  constructor(message: string) {
    super(message, 409, 'CONFLICT');
  }
}

export class TooManyRequestsError extends AppError {
  constructor(message: string = '请求过于频繁') {
    super(message, 429, 'TOO_MANY_REQUESTS');
  }
}

/**
 * Prisma错误处理
 */
function handlePrismaError(error: any): AppError {
  switch (error.code) {
    case 'P2002':
      // 唯一约束违反
      const field = error.meta?.target?.[0] || '字段';
      return new ConflictError(`${field} 已存在`);
    
    case 'P2025':
      // 记录未找到
      return new NotFoundError('记录');
    
    case 'P2003':
      // 外键约束违反
      return new ValidationError('关联数据不存在');
    
    case 'P2014':
      // 关联记录不存在
      return new ValidationError('关联记录不存在');
    
    default:
      return new AppError('数据库操作失败', 500, 'DATABASE_ERROR');
  }
}

/**
 * JWT错误处理
 */
function handleJWTError(error: any): AppError {
  if (error.name === 'JsonWebTokenError') {
    return new UnauthorizedError('无效的访问令牌');
  }
  
  if (error.name === 'TokenExpiredError') {
    return new UnauthorizedError('访问令牌已过期');
  }
  
  return new UnauthorizedError('令牌验证失败');
}

/**
 * 验证错误处理
 */
function handleValidationError(error: any): AppError {
  if (error.details) {
    // Joi验证错误
    const message = error.details.map((detail: any) => detail.message).join(', ');
    return new ValidationError(message);
  }
  
  if (error.errors) {
    // Express-validator错误
    const message = error.errors.map((err: any) => err.msg).join(', ');
    return new ValidationError(message);
  }
  
  return new ValidationError('数据验证失败');
}

/**
 * 文件上传错误处理
 */
function handleMulterError(error: any): AppError {
  switch (error.code) {
    case 'LIMIT_FILE_SIZE':
      return new ValidationError('文件大小超出限制');
    
    case 'LIMIT_FILE_COUNT':
      return new ValidationError('文件数量超出限制');
    
    case 'LIMIT_UNEXPECTED_FILE':
      return new ValidationError('不支持的文件字段');
    
    default:
      return new ValidationError('文件上传失败');
  }
}

/**
 * 错误响应格式
 */
interface ErrorResponse {
  success: false;
  error: {
    message: string;
    code?: string;
    statusCode: number;
    details?: any;
    stack?: string;
  };
  timestamp: string;
  path: string;
  requestId?: string;
}

/**
 * 全局错误处理中间件
 */
export function errorHandler(
  error: any,
  req: Request,
  res: Response,
  next: NextFunction
): void {
  let appError: AppError;

  // 处理不同类型的错误
  if (error instanceof AppError) {
    appError = error;
  } else if (error.code?.startsWith('P')) {
    // Prisma错误
    appError = handlePrismaError(error);
  } else if (error.name?.includes('JWT') || error.name?.includes('Token')) {
    // JWT错误
    appError = handleJWTError(error);
  } else if (error.isJoi || error.errors) {
    // 验证错误
    appError = handleValidationError(error);
  } else if (error.code?.startsWith('LIMIT_')) {
    // Multer错误
    appError = handleMulterError(error);
  } else {
    // 未知错误
    appError = new AppError(
      config.env === 'production' ? '服务器内部错误' : error.message,
      500,
      'INTERNAL_SERVER_ERROR'
    );
  }

  // 记录错误日志
  const logContext = {
    requestId: req.requestId,
    userId: req.user?.id,
    method: req.method,
    url: req.url,
    ip: req.ip,
    userAgent: req.get('User-Agent'),
    statusCode: appError.statusCode,
    errorCode: appError.code,
    stack: error.stack,
  };

  if (appError.statusCode >= 500) {
    logger.error(appError.message, logContext);
  } else {
    logger.warn(appError.message, logContext);
  }

  // 构建错误响应
  const errorResponse: ErrorResponse = {
    success: false,
    error: {
      message: appError.message,
      code: appError.code,
      statusCode: appError.statusCode,
    },
    timestamp: new Date().toISOString(),
    path: req.path,
    requestId: req.requestId,
  };

  // 在开发环境中包含堆栈信息
  if (config.env === 'development') {
    errorResponse.error.stack = error.stack;
    errorResponse.error.details = error;
  }

  // 发送错误响应
  res.status(appError.statusCode).json(errorResponse);
}

/**
 * 异步错误处理包装器
 */
export function asyncHandler<T extends Request, U extends Response>(
  fn: (req: T, res: U, next: NextFunction) => Promise<any>
) {
  return (req: T, res: U, next: NextFunction) => {
    Promise.resolve(fn(req, res, next)).catch(next);
  };
}

/**
 * 404错误处理中间件
 */
export function notFoundHandler(req: Request, res: Response, next: NextFunction): void {
  const error = new NotFoundError(`路由 ${req.originalUrl}`);
  next(error);
}

/**
 * 创建错误响应
 */
export function createErrorResponse(
  message: string,
  statusCode: number = 500,
  code?: string
): never {
  throw new AppError(message, statusCode, code);
}

/**
 * 验证错误响应
 */
export function createValidationError(message: string, code?: string): never {
  throw new ValidationError(message, code);
}

/**
 * 未找到错误响应
 */
export function createNotFoundError(resource: string): never {
  throw new NotFoundError(resource);
}

/**
 * 未授权错误响应
 */
export function createUnauthorizedError(message?: string): never {
  throw new UnauthorizedError(message);
}

/**
 * 禁止访问错误响应
 */
export function createForbiddenError(message?: string): never {
  throw new ForbiddenError(message);
}

/**
 * 冲突错误响应
 */
export function createConflictError(message: string): never {
  throw new ConflictError(message);
}
