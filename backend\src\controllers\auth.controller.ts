/**
 * 认证控制器
 */

import { Request, Response } from 'express';
import bcrypt from 'bcrypt';
import { DatabaseManager } from '../config/database';
import { config } from '../config';
import { logger } from '../utils/logger';
import { 
  generateToken, 
  generateRefreshToken, 
  verifyRefreshToken 
} from '../middleware/auth';
import { 
  UnauthorizedError, 
  ConflictError, 
  NotFoundError,
  ValidationError 
} from '../middleware/errorHandler';
import { ApiResponse } from '../utils/response';
import { EmailService } from '../services/email.service';
import { UserRole } from '@prisma/client';

/**
 * 认证控制器
 */
export class AuthController {
  private prisma = DatabaseManager.getInstance().getPrisma();
  private emailService = new EmailService();

  /**
   * 用户登录
   */
  public async login(req: Request, res: Response): Promise<void> {
    const { email, password } = req.body;

    // 查找用户
    const user = await this.prisma.user.findUnique({
      where: { email },
      select: {
        id: true,
        email: true,
        password: true,
        name: true,
        avatar: true,
        role: true,
        isActive: true,
        lastLoginAt: true,
      },
    });

    if (!user) {
      throw new UnauthorizedError('邮箱或密码错误');
    }

    if (!user.isActive) {
      throw new UnauthorizedError('账户已被禁用');
    }

    // 验证密码
    const isPasswordValid = await bcrypt.compare(password, user.password);
    if (!isPasswordValid) {
      throw new UnauthorizedError('邮箱或密码错误');
    }

    // 生成令牌
    const tokenPayload = {
      userId: user.id,
      email: user.email,
      role: user.role,
    };

    const accessToken = generateToken(tokenPayload);
    const refreshToken = generateRefreshToken(tokenPayload);

    // 保存刷新令牌到数据库
    await this.prisma.userSession.create({
      data: {
        userId: user.id,
        refreshToken,
        userAgent: req.get('User-Agent'),
        ipAddress: req.ip,
        expiresAt: new Date(Date.now() + 30 * 24 * 60 * 60 * 1000), // 30天
      },
    });

    // 更新最后登录时间
    await this.prisma.user.update({
      where: { id: user.id },
      data: { lastLoginAt: new Date() },
    });

    // 记录登录日志
    logger.info('用户登录成功', {
      userId: user.id,
      email: user.email,
      ip: req.ip,
      userAgent: req.get('User-Agent'),
    });

    // 返回用户信息和令牌
    const userData = {
      id: user.id,
      email: user.email,
      name: user.name,
      avatar: user.avatar,
      role: user.role,
      lastLoginAt: user.lastLoginAt,
    };

    res.json(ApiResponse.success({
      user: userData,
      accessToken,
      refreshToken,
    }, '登录成功'));
  }

  /**
   * 用户注册
   */
  public async register(req: Request, res: Response): Promise<void> {
    const { email, password, name } = req.body;

    // 检查邮箱是否已存在
    const existingUser = await this.prisma.user.findUnique({
      where: { email },
    });

    if (existingUser) {
      throw new ConflictError('邮箱已被注册');
    }

    // 加密密码
    const hashedPassword = await bcrypt.hash(password, config.security.bcryptRounds);

    // 创建用户
    const user = await this.prisma.user.create({
      data: {
        email,
        password: hashedPassword,
        name,
        role: UserRole.USER,
      },
      select: {
        id: true,
        email: true,
        name: true,
        role: true,
        createdAt: true,
      },
    });

    // 记录注册日志
    logger.info('用户注册成功', {
      userId: user.id,
      email: user.email,
      ip: req.ip,
      userAgent: req.get('User-Agent'),
    });

    // 发送欢迎邮件
    try {
      await this.emailService.sendWelcomeEmail(user.email, user.name);
    } catch (error) {
      logger.warn('发送欢迎邮件失败', { error, userId: user.id });
    }

    res.status(201).json(ApiResponse.success(user, '注册成功'));
  }

  /**
   * 用户登出
   */
  public async logout(req: Request, res: Response): Promise<void> {
    const refreshToken = req.body.refreshToken || req.cookies.refreshToken;

    if (refreshToken) {
      // 删除刷新令牌
      await this.prisma.userSession.deleteMany({
        where: {
          refreshToken,
          userId: req.user!.id,
        },
      });
    }

    // 记录登出日志
    logger.info('用户登出', {
      userId: req.user!.id,
      email: req.user!.email,
      ip: req.ip,
    });

    res.json(ApiResponse.success(null, '登出成功'));
  }

  /**
   * 刷新访问令牌
   */
  public async refresh(req: Request, res: Response): Promise<void> {
    const { refreshToken } = req.body;

    if (!refreshToken) {
      throw new UnauthorizedError('缺少刷新令牌');
    }

    // 验证刷新令牌
    const payload = verifyRefreshToken(refreshToken);

    // 检查刷新令牌是否存在于数据库中
    const session = await this.prisma.userSession.findUnique({
      where: { refreshToken },
      include: {
        user: {
          select: {
            id: true,
            email: true,
            name: true,
            role: true,
            isActive: true,
          },
        },
      },
    });

    if (!session || !session.user.isActive) {
      throw new UnauthorizedError('刷新令牌无效');
    }

    // 检查是否过期
    if (session.expiresAt < new Date()) {
      // 删除过期的会话
      await this.prisma.userSession.delete({
        where: { id: session.id },
      });
      throw new UnauthorizedError('刷新令牌已过期');
    }

    // 生成新的访问令牌
    const newTokenPayload = {
      userId: session.user.id,
      email: session.user.email,
      role: session.user.role,
    };

    const newAccessToken = generateToken(newTokenPayload);

    res.json(ApiResponse.success({
      accessToken: newAccessToken,
      user: session.user,
    }, '令牌刷新成功'));
  }

  /**
   * 获取用户信息
   */
  public async getProfile(req: Request, res: Response): Promise<void> {
    const user = await this.prisma.user.findUnique({
      where: { id: req.user!.id },
      select: {
        id: true,
        email: true,
        name: true,
        avatar: true,
        role: true,
        isActive: true,
        lastLoginAt: true,
        createdAt: true,
        updatedAt: true,
      },
    });

    if (!user) {
      throw new NotFoundError('用户');
    }

    res.json(ApiResponse.success(user, '获取用户信息成功'));
  }

  /**
   * 更新用户信息
   */
  public async updateProfile(req: Request, res: Response): Promise<void> {
    const { name, avatar } = req.body;

    const updatedUser = await this.prisma.user.update({
      where: { id: req.user!.id },
      data: {
        ...(name && { name }),
        ...(avatar && { avatar }),
      },
      select: {
        id: true,
        email: true,
        name: true,
        avatar: true,
        role: true,
        updatedAt: true,
      },
    });

    logger.info('用户信息更新', {
      userId: req.user!.id,
      changes: { name, avatar },
    });

    res.json(ApiResponse.success(updatedUser, '用户信息更新成功'));
  }

  /**
   * 修改密码
   */
  public async changePassword(req: Request, res: Response): Promise<void> {
    const { currentPassword, newPassword } = req.body;

    // 获取用户当前密码
    const user = await this.prisma.user.findUnique({
      where: { id: req.user!.id },
      select: { password: true },
    });

    if (!user) {
      throw new NotFoundError('用户');
    }

    // 验证当前密码
    const isCurrentPasswordValid = await bcrypt.compare(currentPassword, user.password);
    if (!isCurrentPasswordValid) {
      throw new ValidationError('当前密码错误');
    }

    // 加密新密码
    const hashedNewPassword = await bcrypt.hash(newPassword, config.security.bcryptRounds);

    // 更新密码
    await this.prisma.user.update({
      where: { id: req.user!.id },
      data: { password: hashedNewPassword },
    });

    // 删除所有会话（强制重新登录）
    await this.prisma.userSession.deleteMany({
      where: { userId: req.user!.id },
    });

    logger.info('用户密码修改', {
      userId: req.user!.id,
      email: req.user!.email,
    });

    res.json(ApiResponse.success(null, '密码修改成功，请重新登录'));
  }

  /**
   * 请求重置密码
   */
  public async resetPassword(req: Request, res: Response): Promise<void> {
    const { email } = req.body;

    const user = await this.prisma.user.findUnique({
      where: { email },
      select: { id: true, name: true, email: true },
    });

    if (!user) {
      throw new NotFoundError('邮箱不存在');
    }

    // 生成重置令牌
    const resetToken = generateToken({
      userId: user.id,
      email: user.email,
      role: UserRole.USER, // 临时角色
    });

    // 发送重置密码邮件
    await this.emailService.sendPasswordResetEmail(user.email, user.name, resetToken);

    logger.info('密码重置请求', {
      userId: user.id,
      email: user.email,
      ip: req.ip,
    });

    res.json(ApiResponse.success(null, '重置密码邮件已发送'));
  }

  /**
   * 确认重置密码
   */
  public async confirmResetPassword(req: Request, res: Response): Promise<void> {
    const { token, password } = req.body;

    try {
      // 验证重置令牌
      const payload = verifyRefreshToken(token);

      // 加密新密码
      const hashedPassword = await bcrypt.hash(password, config.security.bcryptRounds);

      // 更新密码
      await this.prisma.user.update({
        where: { id: payload.userId },
        data: { password: hashedPassword },
      });

      // 删除所有会话
      await this.prisma.userSession.deleteMany({
        where: { userId: payload.userId },
      });

      logger.info('密码重置成功', {
        userId: payload.userId,
        email: payload.email,
        ip: req.ip,
      });

      res.json(ApiResponse.success(null, '密码重置成功'));
    } catch (error) {
      throw new ValidationError('重置令牌无效或已过期');
    }
  }
}
