/**
 * 产品路由
 */

import { Router } from 'express';
import { body, query } from 'express-validator';
import { ProductController } from '../controllers/product.controller';
import { authMiddleware, requireAdmin, requireEditor } from '../middleware/auth';
import { asyncHandler } from '../middleware/errorHandler';
import { validateRequest } from '../middleware/validation';

const router = Router();
const productController = new ProductController();

/**
 * 产品创建验证规则
 */
const createProductValidation = [
  body('name')
    .trim()
    .isLength({ min: 2, max: 255 })
    .withMessage('产品名称长度必须在2-255个字符之间'),
  body('slug')
    .trim()
    .isLength({ min: 2, max: 255 })
    .matches(/^[a-z0-9]+(?:-[a-z0-9]+)*$/)
    .withMessage('产品标识符格式不正确'),
  body('price')
    .isFloat({ min: 0 })
    .withMessage('价格必须大于等于0'),
  body('categoryId')
    .isInt({ min: 1 })
    .withMessage('分类ID必须是正整数'),
  body('description')
    .optional()
    .isLength({ max: 5000 })
    .withMessage('产品描述不能超过5000个字符'),
  body('shortDesc')
    .optional()
    .isLength({ max: 500 })
    .withMessage('简短描述不能超过500个字符'),
  body('sku')
    .optional()
    .isLength({ max: 100 })
    .withMessage('SKU不能超过100个字符'),
  body('brand')
    .optional()
    .isLength({ max: 100 })
    .withMessage('品牌不能超过100个字符'),
  body('model')
    .optional()
    .isLength({ max: 100 })
    .withMessage('型号不能超过100个字符'),
];

/**
 * 产品更新验证规则
 */
const updateProductValidation = [
  body('name')
    .optional()
    .trim()
    .isLength({ min: 2, max: 255 })
    .withMessage('产品名称长度必须在2-255个字符之间'),
  body('slug')
    .optional()
    .trim()
    .isLength({ min: 2, max: 255 })
    .matches(/^[a-z0-9]+(?:-[a-z0-9]+)*$/)
    .withMessage('产品标识符格式不正确'),
  body('price')
    .optional()
    .isFloat({ min: 0 })
    .withMessage('价格必须大于等于0'),
  body('categoryId')
    .optional()
    .isInt({ min: 1 })
    .withMessage('分类ID必须是正整数'),
];

/**
 * 查询验证规则
 */
const queryValidation = [
  query('page')
    .optional()
    .isInt({ min: 1 })
    .withMessage('页码必须是正整数'),
  query('limit')
    .optional()
    .isInt({ min: 1, max: 100 })
    .withMessage('每页数量必须在1-100之间'),
  query('minPrice')
    .optional()
    .isFloat({ min: 0 })
    .withMessage('最低价格必须大于等于0'),
  query('maxPrice')
    .optional()
    .isFloat({ min: 0 })
    .withMessage('最高价格必须大于等于0'),
];

/**
 * @swagger
 * /api/products:
 *   get:
 *     summary: 获取产品列表
 *     tags: [产品]
 *     parameters:
 *       - in: query
 *         name: page
 *         schema:
 *           type: integer
 *           minimum: 1
 *         description: 页码
 *       - in: query
 *         name: limit
 *         schema:
 *           type: integer
 *           minimum: 1
 *           maximum: 100
 *         description: 每页数量
 *       - in: query
 *         name: category
 *         schema:
 *           type: string
 *         description: 分类标识符
 *       - in: query
 *         name: search
 *         schema:
 *           type: string
 *         description: 搜索关键词
 *       - in: query
 *         name: sortBy
 *         schema:
 *           type: string
 *           enum: [price, name, created, popular]
 *         description: 排序字段
 *       - in: query
 *         name: sortOrder
 *         schema:
 *           type: string
 *           enum: [asc, desc]
 *         description: 排序方向
 *       - in: query
 *         name: featured
 *         schema:
 *           type: boolean
 *         description: 是否特色产品
 *       - in: query
 *         name: minPrice
 *         schema:
 *           type: number
 *         description: 最低价格
 *       - in: query
 *         name: maxPrice
 *         schema:
 *           type: number
 *         description: 最高价格
 *       - in: query
 *         name: brand
 *         schema:
 *           type: string
 *         description: 品牌
 *     responses:
 *       200:
 *         description: 获取成功
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                 data:
 *                   type: object
 *                   properties:
 *                     data:
 *                       type: array
 *                       items:
 *                         $ref: '#/components/schemas/Product'
 *                     pagination:
 *                       $ref: '#/components/schemas/Pagination'
 */
router.get('/',
  queryValidation,
  validateRequest,
  asyncHandler(productController.getProducts.bind(productController))
);

/**
 * @swagger
 * /api/products/featured:
 *   get:
 *     summary: 获取特色产品
 *     tags: [产品]
 *     parameters:
 *       - in: query
 *         name: limit
 *         schema:
 *           type: integer
 *           minimum: 1
 *           maximum: 50
 *         description: 数量限制
 *     responses:
 *       200:
 *         description: 获取成功
 */
router.get('/featured',
  asyncHandler(productController.getFeaturedProducts.bind(productController))
);

/**
 * @swagger
 * /api/products/popular:
 *   get:
 *     summary: 获取热门产品
 *     tags: [产品]
 *     parameters:
 *       - in: query
 *         name: limit
 *         schema:
 *           type: integer
 *           minimum: 1
 *           maximum: 50
 *         description: 数量限制
 *     responses:
 *       200:
 *         description: 获取成功
 */
router.get('/popular',
  asyncHandler(productController.getPopularProducts.bind(productController))
);

/**
 * @swagger
 * /api/products/search:
 *   get:
 *     summary: 搜索产品
 *     tags: [产品]
 *     parameters:
 *       - in: query
 *         name: q
 *         required: true
 *         schema:
 *           type: string
 *         description: 搜索关键词
 *       - in: query
 *         name: page
 *         schema:
 *           type: integer
 *           minimum: 1
 *         description: 页码
 *       - in: query
 *         name: limit
 *         schema:
 *           type: integer
 *           minimum: 1
 *           maximum: 100
 *         description: 每页数量
 *     responses:
 *       200:
 *         description: 搜索成功
 *       400:
 *         description: 搜索关键词不能为空
 */
router.get('/search',
  query('q').notEmpty().withMessage('搜索关键词不能为空'),
  queryValidation,
  validateRequest,
  asyncHandler(productController.searchProducts.bind(productController))
);

/**
 * @swagger
 * /api/products:
 *   post:
 *     summary: 创建产品
 *     tags: [产品]
 *     security:
 *       - bearerAuth: []
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             $ref: '#/components/schemas/CreateProductDto'
 *     responses:
 *       201:
 *         description: 创建成功
 *       400:
 *         description: 请求参数错误
 *       401:
 *         description: 未授权
 *       403:
 *         description: 权限不足
 */
router.post('/',
  authMiddleware,
  requireEditor,
  createProductValidation,
  validateRequest,
  asyncHandler(productController.createProduct.bind(productController))
);

/**
 * @swagger
 * /api/products/{slug}:
 *   get:
 *     summary: 获取产品详情
 *     tags: [产品]
 *     parameters:
 *       - in: path
 *         name: slug
 *         required: true
 *         schema:
 *           type: string
 *         description: 产品标识符
 *     responses:
 *       200:
 *         description: 获取成功
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                 data:
 *                   $ref: '#/components/schemas/ProductDetail'
 *       404:
 *         description: 产品不存在
 */
router.get('/:slug',
  asyncHandler(productController.getProduct.bind(productController))
);

/**
 * @swagger
 * /api/products/{id}:
 *   put:
 *     summary: 更新产品
 *     tags: [产品]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: id
 *         required: true
 *         schema:
 *           type: integer
 *         description: 产品ID
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             $ref: '#/components/schemas/UpdateProductDto'
 *     responses:
 *       200:
 *         description: 更新成功
 *       400:
 *         description: 请求参数错误
 *       401:
 *         description: 未授权
 *       403:
 *         description: 权限不足
 *       404:
 *         description: 产品不存在
 */
router.put('/:id',
  authMiddleware,
  requireEditor,
  updateProductValidation,
  validateRequest,
  asyncHandler(productController.updateProduct.bind(productController))
);

/**
 * @swagger
 * /api/products/{id}:
 *   delete:
 *     summary: 删除产品
 *     tags: [产品]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: id
 *         required: true
 *         schema:
 *           type: integer
 *         description: 产品ID
 *     responses:
 *       200:
 *         description: 删除成功
 *       401:
 *         description: 未授权
 *       403:
 *         description: 权限不足
 *       404:
 *         description: 产品不存在
 */
router.delete('/:id',
  authMiddleware,
  requireAdmin,
  asyncHandler(productController.deleteProduct.bind(productController))
);

export default router;
