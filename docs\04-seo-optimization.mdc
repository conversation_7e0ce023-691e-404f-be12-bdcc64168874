---
description: 
globs: 
alwaysApply: true
---
# SEO优化规范

## Nuxt SEO 配置规范

### 全局SEO配置
```typescript
// nuxt.config.ts
export default defineNuxtConfig({
  // SEO模块配置
  modules: [
    '@nuxtjs/seo',
    '@nuxt/image',
    '@nuxtjs/sitemap',
    '@nuxtjs/robots'
  ],

  // 站点配置
  site: {
    url: 'https://www.yourpos.com',
    name: 'POS机产品展示网站',
    description: '专业的POS机产品展示平台，提供最新的支付终端设备信息',
    defaultLocale: 'zh-CN'
  },

  // SEO配置
  seo: {
    redirectToCanonicalSiteUrl: true,
    fallbackTitle: 'POS机产品展示网站',
    templateParams: {
      separator: ' | ',
      siteName: 'POS机产品展示网站'
    }
  },

  // 站点地图配置
  sitemap: {
    hostname: 'https://www.yourpos.com',
    gzip: true,
    routes: async () => {
      // 动态生成产品页面路由
      const products = await $fetch('/api/products');
      return products.map(product => `/products/${product.slug}`);
    }
  },

  // robots.txt配置
  robots: {
    UserAgent: '*',
    Allow: '/',
    Disallow: ['/admin', '/api'],
    Sitemap: 'https://www.yourpos.com/sitemap.xml'
  }
});
```

### 页面级SEO配置
```vue
<!-- pages/index.vue -->
<template>
  <div>
    <!-- 页面内容 -->
  </div>
</template>

<script setup lang="ts">
// 首页SEO配置
useSeoMeta({
  title: 'POS机产品展示 - 专业支付终端设备',
  description: '提供最新最全的POS机产品信息，包括移动POS、智能POS、传统POS等各类支付终端设备',
  keywords: 'POS机,支付终端,移动支付,智能POS,收银设备',
  author: '公司名称',
  robots: 'index,follow',
  
  // Open Graph
  ogTitle: 'POS机产品展示 - 专业支付终端设备',
  ogDescription: '提供最新最全的POS机产品信息，包括移动POS、智能POS、传统POS等各类支付终端设备',
  ogImage: '/images/og-image.jpg',
  ogUrl: 'https://www.yourpos.com',
  ogType: 'website',
  ogSiteName: 'POS机产品展示网站',
  
  // Twitter Card
  twitterCard: 'summary_large_image',
  twitterSite: '@yourpos',
  twitterCreator: '@yourpos',
  twitterTitle: 'POS机产品展示 - 专业支付终端设备',
  twitterDescription: '提供最新最全的POS机产品信息',
  twitterImage: '/images/twitter-image.jpg'
});

// 结构化数据
useSchemaOrg([
  defineOrganization({
    name: '公司名称',
    url: 'https://www.yourpos.com',
    logo: 'https://www.yourpos.com/logo.png',
    sameAs: [
      'https://www.facebook.com/yourpos',
      'https://www.twitter.com/yourpos',
      'https://www.linkedin.com/company/yourpos'
    ]
  }),
  defineWebSite({
    name: 'POS机产品展示网站',
    url: 'https://www.yourpos.com',
    potentialAction: {
      '@type': 'SearchAction',
      target: 'https://www.yourpos.com/search?q={search_term_string}',
      'query-input': 'required name=search_term_string'
    }
  })
]);
</script>
```

### 产品页面SEO配置
```vue
<!-- pages/products/[slug].vue -->
<template>
  <div>
    <!-- 产品详情内容 -->
  </div>
</template>

<script setup lang="ts">
const route = useRoute();
const { data: product } = await $fetch(`/api/products/${route.params.slug}`);

// 产品页面SEO配置
useSeoMeta({
  title: `${product.name} - POS机产品详情`,
  description: product.description || `${product.name}详细信息，包括产品特性、技术参数、价格等`,
  keywords: `${product.name},POS机,${product.category.name},支付终端`,
  
  // Open Graph
  ogTitle: `${product.name} - POS机产品详情`,
  ogDescription: product.description,
  ogImage: product.images[0]?.url || '/images/default-product.jpg',
  ogUrl: `https://www.yourpos.com/products/${product.slug}`,
  ogType: 'product',
  
  // 产品特定meta
  'product:price:amount': product.price,
  'product:price:currency': 'CNY'
});

// 产品结构化数据
useSchemaOrg([
  defineProduct({
    name: product.name,
    description: product.description,
    image: product.images.map(img => img.url),
    brand: {
      '@type': 'Brand',
      name: '品牌名称'
    },
    offers: {
      '@type': 'Offer',
      price: product.price,
      priceCurrency: 'CNY',
      availability: 'https://schema.org/InStock',
      seller: {
        '@type': 'Organization',
        name: '公司名称'
      }
    },
    category: product.category.name,
    sku: product.sku,
    mpn: product.mpn
  })
]);
</script>
```

## 技术SEO优化

### 页面性能优化
```typescript
// nuxt.config.ts
export default defineNuxtConfig({
  // 图片优化
  image: {
    quality: 80,
    format: ['webp', 'jpg'],
    screens: {
      xs: 320,
      sm: 640,
      md: 768,
      lg: 1024,
      xl: 1280,
      xxl: 1536
    },
    presets: {
      avatar: {
        modifiers: {
          format: 'webp',
          width: 50,
          height: 50
        }
      },
      product: {
        modifiers: {
          format: 'webp',
          width: 400,
          height: 300,
          quality: 85
        }
      }
    }
  },

  // 预渲染配置
  nitro: {
    prerender: {
      routes: [
        '/',
        '/about',
        '/products',
        '/contact',
        '/sitemap.xml'
      ]
    }
  },

  // 压缩配置
  compression: {
    gzip: true,
    brotli: true
  }
});
```

### 图片优化组件
```vue
<!-- components/common/OptimizedImage.vue -->
<template>
  <NuxtImg
    :src="src"
    :alt="alt"
    :width="width"
    :height="height"
    :loading="loading"
    :preset="preset"
    :sizes="sizes"
    :class="imageClass"
    @load="onLoad"
    @error="onError"
  />
</template>

<script setup lang="ts">
interface Props {
  src: string;
  alt: string;
  width?: number;
  height?: number;
  loading?: 'lazy' | 'eager';
  preset?: string;
  sizes?: string;
  imageClass?: string;
}

const props = withDefaults(defineProps<Props>(), {
  loading: 'lazy',
  preset: 'default'
});

const emit = defineEmits<{
  load: [event: Event];
  error: [event: Event];
}>();

const onLoad = (event: Event) => {
  emit('load', event);
};

const onError = (event: Event) => {
  emit('error', event);
};
</script>
```

### 面包屑导航组件
```vue
<!-- components/common/Breadcrumb.vue -->
<template>
  <nav aria-label="面包屑导航" class="breadcrumb">
    <ol class="breadcrumb-list">
      <li
        v-for="(item, index) in breadcrumbItems"
        :key="index"
        class="breadcrumb-item"
      >
        <NuxtLink
          v-if="item.href && index < breadcrumbItems.length - 1"
          :to="item.href"
          class="breadcrumb-link"
        >
          {{ item.name }}
        </NuxtLink>
        <span v-else class="breadcrumb-current">
          {{ item.name }}
        </span>
        <span
          v-if="index < breadcrumbItems.length - 1"
          class="breadcrumb-separator"
          aria-hidden="true"
        >
          /
        </span>
      </li>
    </ol>
  </nav>
</template>

<script setup lang="ts">
interface BreadcrumbItem {
  name: string;
  href?: string;
}

interface Props {
  items: BreadcrumbItem[];
}

const props = defineProps<Props>();

// 添加首页到面包屑
const breadcrumbItems = computed(() => [
  { name: '首页', href: '/' },
  ...props.items
]);

// 结构化数据
useSchemaOrg([
  defineBreadcrumbList({
    itemListElement: breadcrumbItems.value.map((item, index) => ({
      '@type': 'ListItem',
      position: index + 1,
      name: item.name,
      item: item.href ? `https://www.yourpos.com${item.href}` : undefined
    }))
  })
]);
</script>

<style scoped>
.breadcrumb {
  @apply mb-4;
}

.breadcrumb-list {
  @apply flex items-center space-x-2 text-sm text-gray-600;
}

.breadcrumb-link {
  @apply text-blue-600 hover:text-blue-800 transition-colors;
}

.breadcrumb-current {
  @apply text-gray-900 font-medium;
}

.breadcrumb-separator {
  @apply text-gray-400;
}
</style>
```

## 内容SEO优化

### 关键词策略
```typescript
// composables/useSEOKeywords.ts
export const useSEOKeywords = () => {
  // 主要关键词
  const primaryKeywords = [
    'POS机',
    '支付终端',
    '收银设备',
    '移动支付',
    '智能POS'
  ];

  // 长尾关键词
  const longTailKeywords = [
    '移动POS机价格',
    '智能POS机功能',
    '无线POS机推荐',
    'POS机办理流程',
    '餐饮POS系统'
  ];

  // 地域关键词
  const locationKeywords = [
    '北京POS机',
    '上海POS机',
    '广州POS机',
    '深圳POS机'
  ];

  // 生成页面关键词
  const generatePageKeywords = (
    pageType: string,
    specificKeywords: string[] = []
  ): string => {
    const baseKeywords = [...primaryKeywords, ...specificKeywords];
    
    switch (pageType) {
      case 'product':
        return [...baseKeywords, ...longTailKeywords].join(',');
      case 'category':
        return [...baseKeywords, ...locationKeywords].join(',');
      default:
        return baseKeywords.join(',');
    }
  };

  return {
    primaryKeywords,
    longTailKeywords,
    locationKeywords,
    generatePageKeywords
  };
};
```

### 内容优化指南
```typescript
// utils/seoContent.ts
export interface SEOContentGuidelines {
  title: {
    minLength: number;
    maxLength: number;
    includeKeyword: boolean;
  };
  description: {
    minLength: number;
    maxLength: number;
    includeKeyword: boolean;
  };
  headings: {
    h1Count: number;
    h2MinCount: number;
    includeKeywords: boolean;
  };
  content: {
    minWordCount: number;
    keywordDensity: number;
    includeInternalLinks: boolean;
  };
}

export const seoGuidelines: SEOContentGuidelines = {
  title: {
    minLength: 30,
    maxLength: 60,
    includeKeyword: true
  },
  description: {
    minLength: 120,
    maxLength: 160,
    includeKeyword: true
  },
  headings: {
    h1Count: 1,
    h2MinCount: 2,
    includeKeywords: true
  },
  content: {
    minWordCount: 300,
    keywordDensity: 0.02, // 2%
    includeInternalLinks: true
  }
};

/**
 * 验证SEO内容质量
 * @param content - 内容对象
 * @returns 验证结果
 */
export const validateSEOContent = (content: {
  title: string;
  description: string;
  body: string;
  keywords: string[];
}) => {
  const issues: string[] = [];
  
  // 验证标题
  if (content.title.length < seoGuidelines.title.minLength) {
    issues.push(`标题过短，建议至少${seoGuidelines.title.minLength}个字符`);
  }
  
  if (content.title.length > seoGuidelines.title.maxLength) {
    issues.push(`标题过长，建议不超过${seoGuidelines.title.maxLength}个字符`);
  }
  
  // 验证描述
  if (content.description.length < seoGuidelines.description.minLength) {
    issues.push(`描述过短，建议至少${seoGuidelines.description.minLength}个字符`);
  }
  
  // 验证关键词密度
  const wordCount = content.body.split(/\s+/).length;
  const keywordCount = content.keywords.reduce((count, keyword) => {
    const regex = new RegExp(keyword, 'gi');
    const matches = content.body.match(regex);
    return count + (matches ? matches.length : 0);
  }, 0);
  
  const density = keywordCount / wordCount;
  if (density > seoGuidelines.content.keywordDensity) {
    issues.push('关键词密度过高，可能被视为关键词堆砌');
  }
  
  return {
    isValid: issues.length === 0,
    issues,
    score: Math.max(0, 100 - issues.length * 10)
  };
};
```

## 技术实现

### 自动生成sitemap
```typescript
// server/api/sitemap.xml.ts
export default defineEventHandler(async (event) => {
  const baseUrl = 'https://www.yourpos.com';
  
  // 获取所有页面数据
  const [products, news, categories] = await Promise.all([
    $fetch('/api/products'),
    $fetch('/api/news'),
    $fetch('/api/categories')
  ]);
  
  // 生成sitemap XML
  const sitemap = `<?xml version="1.0" encoding="UTF-8"?>
<urlset xmlns="http://www.sitemaps.org/schemas/sitemap/0.9">
  <!-- 静态页面 -->
  <url>
    <loc>${baseUrl}/</loc>
    <lastmod>${new Date().toISOString()}</lastmod>
    <changefreq>daily</changefreq>
    <priority>1.0</priority>
  </url>
  <url>
    <loc>${baseUrl}/about</loc>
    <lastmod>${new Date().toISOString()}</lastmod>
    <changefreq>monthly</changefreq>
    <priority>0.8</priority>
  </url>
  <url>
    <loc>${baseUrl}/products</loc>
    <lastmod>${new Date().toISOString()}</lastmod>
    <changefreq>weekly</changefreq>
    <priority>0.9</priority>
  </url>
  
  <!-- 产品页面 -->
  ${products.map(product => `
  <url>
    <loc>${baseUrl}/products/${product.slug}</loc>
    <lastmod>${product.updatedAt}</lastmod>
    <changefreq>monthly</changefreq>
    <priority>0.7</priority>
  </url>`).join('')}
  
  <!-- 新闻页面 -->
  ${news.map(article => `
  <url>
    <loc>${baseUrl}/news/${article.slug}</loc>
    <lastmod>${article.updatedAt}</lastmod>
    <changefreq>monthly</changefreq>
    <priority>0.6</priority>
  </url>`).join('')}
  
  <!-- 分类页面 -->
  ${categories.map(category => `
  <url>
    <loc>${baseUrl}/products/category/${category.slug}</loc>
    <lastmod>${new Date().toISOString()}</lastmod>
    <changefreq>weekly</changefreq>
    <priority>0.8</priority>
  </url>`).join('')}
</urlset>`;

  setHeader(event, 'Content-Type', 'application/xml');
  return sitemap;
});
```

### 结构化数据管理
```typescript
// composables/useStructuredData.ts
export const useStructuredData = () => {
  /**
   * 生成产品结构化数据
   */
  const generateProductSchema = (product: any) => {
    return {
      '@context': 'https://schema.org',
      '@type': 'Product',
      name: product.name,
      description: product.description,
      image: product.images.map((img: any) => img.url),
      brand: {
        '@type': 'Brand',
        name: '品牌名称'
      },
      offers: {
        '@type': 'Offer',
        price: product.price,
        priceCurrency: 'CNY',
        availability: 'https://schema.org/InStock',
        seller: {
          '@type': 'Organization',
          name: '公司名称'
        }
      },
      aggregateRating: product.rating ? {
        '@type': 'AggregateRating',
        ratingValue: product.rating.average,
        reviewCount: product.rating.count
      } : undefined
    };
  };

  /**
   * 生成文章结构化数据
   */
  const generateArticleSchema = (article: any) => {
    return {
      '@context': 'https://schema.org',
      '@type': 'Article',
      headline: article.title,
      description: article.excerpt,
      image: article.featuredImage,
      author: {
        '@type': 'Person',
        name: article.author.name
      },
      publisher: {
        '@type': 'Organization',
        name: '公司名称',
        logo: {
          '@type': 'ImageObject',
          url: 'https://www.yourpos.com/logo.png'
        }
      },
      datePublished: article.publishedAt,
      dateModified: article.updatedAt
    };
  };

  return {
    generateProductSchema,
    generateArticleSchema
  };
};
```

