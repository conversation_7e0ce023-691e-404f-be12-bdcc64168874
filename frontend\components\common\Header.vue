<template>
  <header class="bg-white shadow-md sticky top-0 z-50">
    <!-- 顶部信息栏 -->
    <div class="bg-gray-50 border-b border-gray-200">
      <div class="container">
        <div class="flex items-center justify-between py-2 text-sm text-gray-600">
          <div class="flex items-center space-x-4">
            <span class="flex items-center">
              <Icon name="heroicons:phone" class="w-4 h-4 mr-1" />
              ************
            </span>
            <span class="flex items-center">
              <Icon name="heroicons:envelope" class="w-4 h-4 mr-1" />
              <EMAIL>
            </span>
          </div>
          <div class="flex items-center space-x-4">
            <NuxtLink to="/login" class="hover:text-blue-600 transition-colors">
              登录
            </NuxtLink>
            <span class="text-gray-300">|</span>
            <NuxtLink to="/register" class="hover:text-blue-600 transition-colors">
              注册
            </NuxtLink>
          </div>
        </div>
      </div>
    </div>

    <!-- 主导航 -->
    <div class="container">
      <div class="flex items-center justify-between py-4">
        <!-- Logo -->
        <NuxtLink to="/" class="flex items-center space-x-2">
          <NuxtImg
            src="/images/logo.png"
            alt="POS机产品展示网站"
            width="40"
            height="40"
            class="w-10 h-10"
          />
          <span class="text-xl font-bold text-gray-900">
            POS机展示
          </span>
        </NuxtLink>

        <!-- 桌面导航菜单 -->
        <nav class="hidden lg:flex items-center space-x-8">
          <NuxtLink
            v-for="item in navigation"
            :key="item.name"
            :to="item.href"
            class="text-gray-700 hover:text-blue-600 font-medium transition-colors relative group"
            :class="{ 'text-blue-600': isActiveRoute(item.href) }"
          >
            {{ item.name }}
            <span
              class="absolute bottom-0 left-0 w-0 h-0.5 bg-blue-600 transition-all duration-300 group-hover:w-full"
              :class="{ 'w-full': isActiveRoute(item.href) }"
            ></span>
          </NuxtLink>
        </nav>

        <!-- 搜索和移动菜单按钮 -->
        <div class="flex items-center space-x-4">
          <!-- 搜索按钮 -->
          <button
            @click="toggleSearch"
            class="p-2 text-gray-600 hover:text-blue-600 transition-colors"
            aria-label="搜索"
          >
            <Icon name="heroicons:magnifying-glass" class="w-5 h-5" />
          </button>

          <!-- 移动菜单按钮 -->
          <button
            @click="toggleMobileMenu"
            class="lg:hidden p-2 text-gray-600 hover:text-blue-600 transition-colors"
            aria-label="菜单"
          >
            <Icon
              :name="isMobileMenuOpen ? 'heroicons:x-mark' : 'heroicons:bars-3'"
              class="w-6 h-6"
            />
          </button>
        </div>
      </div>
    </div>

    <!-- 搜索框 -->
    <Transition
      enter-active-class="transition-all duration-300 ease-out"
      enter-from-class="opacity-0 -translate-y-2"
      enter-to-class="opacity-100 translate-y-0"
      leave-active-class="transition-all duration-200 ease-in"
      leave-from-class="opacity-100 translate-y-0"
      leave-to-class="opacity-0 -translate-y-2"
    >
      <div v-if="isSearchOpen" class="border-t border-gray-200 bg-gray-50">
        <div class="container py-4">
          <div class="relative max-w-md mx-auto">
            <input
              v-model="searchQuery"
              type="text"
              placeholder="搜索产品..."
              class="w-full pl-10 pr-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
              @keyup.enter="handleSearch"
            />
            <Icon
              name="heroicons:magnifying-glass"
              class="absolute left-3 top-1/2 transform -translate-y-1/2 w-5 h-5 text-gray-400"
            />
          </div>
        </div>
      </div>
    </Transition>

    <!-- 移动菜单 -->
    <Transition
      enter-active-class="transition-all duration-300 ease-out"
      enter-from-class="opacity-0 -translate-y-2"
      enter-to-class="opacity-100 translate-y-0"
      leave-active-class="transition-all duration-200 ease-in"
      leave-from-class="opacity-100 translate-y-0"
      leave-to-class="opacity-0 -translate-y-2"
    >
      <div v-if="isMobileMenuOpen" class="lg:hidden border-t border-gray-200 bg-white">
        <nav class="container py-4">
          <div class="space-y-2">
            <NuxtLink
              v-for="item in navigation"
              :key="item.name"
              :to="item.href"
              class="block px-4 py-2 text-gray-700 hover:text-blue-600 hover:bg-gray-50 rounded-md transition-colors"
              :class="{ 'text-blue-600 bg-blue-50': isActiveRoute(item.href) }"
              @click="closeMobileMenu"
            >
              {{ item.name }}
            </NuxtLink>
          </div>
        </nav>
      </div>
    </Transition>
  </header>
</template>

<script setup lang="ts">
// 导航菜单数据
const navigation = [
  { name: '首页', href: '/' },
  { name: '产品中心', href: '/products' },
  { name: '关于我们', href: '/about' },
  { name: '新闻资讯', href: '/news' },
  { name: '联系我们', href: '/contact' }
]

// 响应式状态
const isMobileMenuOpen = ref(false)
const isSearchOpen = ref(false)
const searchQuery = ref('')

// 路由相关
const route = useRoute()

// 判断当前路由是否激活
const isActiveRoute = (href: string): boolean => {
  if (href === '/') {
    return route.path === '/'
  }
  return route.path.startsWith(href)
}

// 切换移动菜单
const toggleMobileMenu = () => {
  isMobileMenuOpen.value = !isMobileMenuOpen.value
  if (isMobileMenuOpen.value) {
    isSearchOpen.value = false
  }
}

// 关闭移动菜单
const closeMobileMenu = () => {
  isMobileMenuOpen.value = false
}

// 切换搜索框
const toggleSearch = () => {
  isSearchOpen.value = !isSearchOpen.value
  if (isSearchOpen.value) {
    isMobileMenuOpen.value = false
  }
}

// 处理搜索
const handleSearch = () => {
  if (searchQuery.value.trim()) {
    navigateTo(`/search?q=${encodeURIComponent(searchQuery.value.trim())}`)
    isSearchOpen.value = false
    searchQuery.value = ''
  }
}

// 监听路由变化，关闭菜单
watch(() => route.path, () => {
  isMobileMenuOpen.value = false
  isSearchOpen.value = false
})

// 监听点击外部区域，关闭菜单
onMounted(() => {
  const handleClickOutside = (event: Event) => {
    const target = event.target as Element
    if (!target.closest('header')) {
      isMobileMenuOpen.value = false
      isSearchOpen.value = false
    }
  }

  document.addEventListener('click', handleClickOutside)
  
  onUnmounted(() => {
    document.removeEventListener('click', handleClickOutside)
  })
})

// 监听ESC键，关闭菜单
onMounted(() => {
  const handleEscape = (event: KeyboardEvent) => {
    if (event.key === 'Escape') {
      isMobileMenuOpen.value = false
      isSearchOpen.value = false
    }
  }

  document.addEventListener('keydown', handleEscape)
  
  onUnmounted(() => {
    document.removeEventListener('keydown', handleEscape)
  })
})
</script>
