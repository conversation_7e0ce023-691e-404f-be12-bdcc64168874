/**
 * 服务器启动入口文件
 */

import { config } from './config';
import { logger } from './utils/logger';
import { DatabaseManager } from './config/database';
import { EmailService } from './services/email.service';
import { startServer } from './app';

/**
 * 初始化应用程序
 */
async function initializeApp(): Promise<void> {
  try {
    logger.info('🚀 开始初始化应用程序...');

    // 初始化数据库连接
    logger.info('📊 初始化数据库连接...');
    const dbManager = DatabaseManager.getInstance();
    await dbManager.connect();
    logger.info('✓ 数据库连接成功');

    // 验证邮件服务配置
    if (config.email.smtp.host) {
      logger.info('📧 验证邮件服务配置...');
      const emailService = new EmailService();
      const isEmailValid = await emailService.verifyConnection();
      if (isEmailValid) {
        logger.info('✓ 邮件服务配置有效');
      } else {
        logger.warn('⚠️ 邮件服务配置无效，邮件功能可能无法正常工作');
      }
    } else {
      logger.warn('⚠️ 未配置邮件服务');
    }

    // 启动HTTP服务器
    logger.info('🌐 启动HTTP服务器...');
    await startServer();

    logger.info('✅ 应用程序初始化完成');

  } catch (error) {
    logger.error('❌ 应用程序初始化失败:', error);
    process.exit(1);
  }
}

/**
 * 主函数
 */
async function main(): Promise<void> {
  // 显示启动信息
  console.log(`
╔══════════════════════════════════════════════════════════════╗
║                                                              ║
║           POS机产品展示网站 API 服务                          ║
║                                                              ║
║  版本: ${(process.env.npm_package_version || '1.0.0').padEnd(10)} 环境: ${config.env.padEnd(10)}                    ║
║                                                              ║
╚══════════════════════════════════════════════════════════════╝
  `);

  // 初始化应用程序
  await initializeApp();
}

// 启动应用程序
main().catch((error) => {
  console.error('启动失败:', error);
  process.exit(1);
});
