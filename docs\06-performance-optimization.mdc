---
description: 
globs: 
alwaysApply: true
---
# 性能优化规范

## 前端性能优化

### Nuxt.js 性能配置
```typescript
// nuxt.config.ts
export default defineNuxtConfig({
  // 实验性功能
  experimental: {
    payloadExtraction: false, // 禁用payload提取以提高性能
    inlineSSRStyles: false,   // 禁用内联SSR样式
    viewTransition: true      // 启用视图过渡
  },

  // 构建优化
  build: {
    transpile: ['@headlessui/vue']
  },

  // Vite配置
  vite: {
    build: {
      rollupOptions: {
        output: {
          manualChunks: {
            vendor: ['vue', 'vue-router'],
            ui: ['@headlessui/vue', '@heroicons/vue']
          }
        }
      }
    },
    optimizeDeps: {
      include: ['vue', 'vue-router', '@headlessui/vue']
    }
  },

  // 路由优化
  router: {
    options: {
      strict: true,
      sensitive: true
    }
  },

  // 预渲染配置
  nitro: {
    prerender: {
      crawlLinks: true,
      routes: [
        '/',
        '/about',
        '/products',
        '/contact'
      ]
    },
    // 压缩配置
    compressPublicAssets: true,
    // 缓存配置
    storage: {
      redis: {
        driver: 'redis',
        host: 'localhost',
        port: 6379,
        db: 0
      }
    }
  },

  // CSS优化
  css: [
    '@/assets/css/main.css'
  ],

  // 图片优化
  image: {
    quality: 80,
    format: ['webp', 'avif', 'jpg'],
    densities: [1, 2],
    sizes: 'sm:100vw md:50vw lg:400px',
    presets: {
      thumbnail: {
        modifiers: {
          format: 'webp',
          width: 150,
          height: 150,
          quality: 70
        }
      },
      hero: {
        modifiers: {
          format: 'webp',
          width: 1200,
          height: 600,
          quality: 85
        }
      }
    }
  }
});
```

### 组件懒加载
```vue
<!-- pages/products/index.vue -->
<template>
  <div>
    <!-- 立即加载的关键内容 -->
    <ProductHero />
    
    <!-- 懒加载的组件 -->
    <LazyProductFilters v-if="showFilters" />
    <LazyProductList :products="products" />
    <LazyProductPagination 
      v-if="totalPages > 1"
      :current-page="currentPage"
      :total-pages="totalPages"
    />
  </div>
</template>

<script setup lang="ts">
// 使用 Lazy 前缀自动懒加载组件
const showFilters = ref(false);

// 延迟显示筛选器
onMounted(() => {
  setTimeout(() => {
    showFilters.value = true;
  }, 100);
});

// 数据预取
const { data: products, pending } = await useLazyFetch('/api/products', {
  key: 'products',
  default: () => [],
  server: true
});
</script>
```

### 图片优化组件
```vue
<!-- components/common/LazyImage.vue -->
<template>
  <div 
    ref="imageContainer"
    :class="['lazy-image-container', { loaded: isLoaded }]"
  >
    <NuxtImg
      v-if="shouldLoad"
      :src="src"
      :alt="alt"
      :width="width"
      :height="height"
      :sizes="sizes"
      :preset="preset"
      :loading="loading"
      :class="imageClass"
      @load="onLoad"
      @error="onError"
    />
    
    <!-- 占位符 -->
    <div 
      v-else
      :class="['placeholder', placeholderClass]"
      :style="{ 
        width: width ? `${width}px` : '100%',
        height: height ? `${height}px` : 'auto',
        aspectRatio: aspectRatio
      }"
    >
      <div class="placeholder-content">
        <svg class="placeholder-icon" viewBox="0 0 24 24">
          <path d="M21 19V5c0-1.1-.9-2-2-2H5c-1.1 0-2 .9-2 2v14c0 1.1.9 2 2 2h14c1.1 0 2-.9 2-2zM8.5 13.5l2.5 3.01L14.5 12l4.5 6H5l3.5-4.5z"/>
        </svg>
      </div>
    </div>
    
    <!-- 加载状态 -->
    <div v-if="isLoading" class="loading-overlay">
      <div class="loading-spinner"></div>
    </div>
  </div>
</template>

<script setup lang="ts">
interface Props {
  src: string;
  alt: string;
  width?: number;
  height?: number;
  sizes?: string;
  preset?: string;
  loading?: 'lazy' | 'eager';
  imageClass?: string;
  placeholderClass?: string;
  aspectRatio?: string;
  threshold?: number;
}

const props = withDefaults(defineProps<Props>(), {
  loading: 'lazy',
  threshold: 0.1,
  aspectRatio: '16/9'
});

const imageContainer = ref<HTMLElement>();
const shouldLoad = ref(props.loading === 'eager');
const isLoaded = ref(false);
const isLoading = ref(false);

// 交叉观察器
let observer: IntersectionObserver | null = null;

const onLoad = () => {
  isLoaded.value = true;
  isLoading.value = false;
};

const onError = () => {
  isLoading.value = false;
};

onMounted(() => {
  if (props.loading === 'lazy' && imageContainer.value) {
    observer = new IntersectionObserver(
      (entries) => {
        entries.forEach((entry) => {
          if (entry.isIntersecting) {
            shouldLoad.value = true;
            isLoading.value = true;
            observer?.unobserve(entry.target);
          }
        });
      },
      { threshold: props.threshold }
    );
    
    observer.observe(imageContainer.value);
  }
});

onUnmounted(() => {
  if (observer) {
    observer.disconnect();
  }
});
</script>

<style scoped>
.lazy-image-container {
  position: relative;
  overflow: hidden;
  transition: opacity 0.3s ease;
}

.placeholder {
  display: flex;
  align-items: center;
  justify-content: center;
  background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
  background-size: 200% 100%;
  animation: shimmer 1.5s infinite;
}

.placeholder-icon {
  width: 48px;
  height: 48px;
  fill: #ccc;
}

.loading-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  display: flex;
  align-items: center;
  justify-content: center;
  background: rgba(255, 255, 255, 0.8);
}

.loading-spinner {
  width: 24px;
  height: 24px;
  border: 2px solid #f3f3f3;
  border-top: 2px solid #3498db;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

@keyframes shimmer {
  0% { background-position: -200% 0; }
  100% { background-position: 200% 0; }
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}
</style>
```

### 数据缓存策略
```typescript
// composables/useCache.ts
export const useCache = () => {
  /**
   * 内存缓存
   */
  const memoryCache = new Map<string, {
    data: any;
    timestamp: number;
    ttl: number;
  }>();

  /**
   * 设置缓存
   */
  const setCache = (key: string, data: any, ttl: number = 300000) => {
    memoryCache.set(key, {
      data,
      timestamp: Date.now(),
      ttl
    });
  };

  /**
   * 获取缓存
   */
  const getCache = (key: string) => {
    const cached = memoryCache.get(key);
    
    if (!cached) return null;
    
    const now = Date.now();
    if (now - cached.timestamp > cached.ttl) {
      memoryCache.delete(key);
      return null;
    }
    
    return cached.data;
  };

  /**
   * 清除缓存
   */
  const clearCache = (pattern?: string) => {
    if (pattern) {
      const regex = new RegExp(pattern);
      for (const key of memoryCache.keys()) {
        if (regex.test(key)) {
          memoryCache.delete(key);
        }
      }
    } else {
      memoryCache.clear();
    }
  };

  /**
   * 缓存装饰器
   */
  const withCache = <T>(
    fn: (...args: any[]) => Promise<T>,
    keyGenerator: (...args: any[]) => string,
    ttl: number = 300000
  ) => {
    return async (...args: any[]): Promise<T> => {
      const key = keyGenerator(...args);
      const cached = getCache(key);
      
      if (cached) {
        return cached;
      }
      
      const result = await fn(...args);
      setCache(key, result, ttl);
      
      return result;
    };
  };

  return {
    setCache,
    getCache,
    clearCache,
    withCache
  };
};

// 使用示例
export const useProducts = () => {
  const { withCache } = useCache();

  const fetchProducts = withCache(
    async (page: number, limit: number) => {
      const { data } = await $fetch('/api/products', {
        query: { page, limit }
      });
      return data;
    },
    (page, limit) => `products:${page}:${limit}`,
    300000 // 5分钟缓存
  );

  return {
    fetchProducts
  };
};
```

## 后端性能优化

### 数据库优化
```typescript
// backend/src/config/database.ts
import { PrismaClient } from '@prisma/client';

/**
 * 数据库连接池配置
 */
export const prisma = new PrismaClient({
  datasources: {
    db: {
      url: process.env.DATABASE_URL
    }
  },
  log: process.env.NODE_ENV === 'development' ? ['query', 'info', 'warn', 'error'] : ['error'],
  errorFormat: 'pretty'
});

// 连接池配置
prisma.$connect();

// 优雅关闭
process.on('beforeExit', async () => {
  await prisma.$disconnect();
});
```

### 查询优化
```typescript
// backend/src/services/product.service.ts
export class ProductService {
  /**
   * 获取产品列表 - 优化版本
   */
  async getProducts(query: IProductQuery): Promise<IProductListResponse> {
    const { page = 1, limit = 10, category, search, sortBy = 'createdAt', sortOrder = 'desc' } = query;
    const skip = (page - 1) * limit;

    // 构建查询条件
    const where: any = {
      isActive: true
    };

    if (category) {
      where.categoryId = category;
    }

    if (search) {
      where.OR = [
        { name: { contains: search, mode: 'insensitive' } },
        { description: { contains: search, mode: 'insensitive' } }
      ];
    }

    // 并行查询数据和总数
    const [products, total] = await Promise.all([
      prisma.product.findMany({
        where,
        skip,
        take: limit,
        include: {
          category: {
            select: {
              id: true,
              name: true,
              slug: true
            }
          },
          images: {
            select: {
              id: true,
              url: true,
              alt: true
            },
            take: 1 // 只取第一张图片
          },
          _count: {
            select: {
              reviews: true
            }
          }
        },
        orderBy: {
          [sortBy]: sortOrder
        }
      }),
      prisma.product.count({ where })
    ]);

    return {
      products,
      pagination: {
        page,
        limit,
        total,
        totalPages: Math.ceil(total / limit)
      }
    };
  }

  /**
   * 获取产品详情 - 优化版本
   */
  async getProductBySlug(slug: string): Promise<IProduct | null> {
    return await prisma.product.findUnique({
      where: { slug },
      include: {
        category: true,
        images: true,
        specifications: true,
        reviews: {
          include: {
            user: {
              select: {
                id: true,
                name: true,
                avatar: true
              }
            }
          },
          orderBy: {
            createdAt: 'desc'
          },
          take: 10
        },
        _count: {
          select: {
            reviews: true
          }
        }
      }
    });
  }

  /**
   * 批量获取产品
   */
  async getProductsByIds(ids: number[]): Promise<IProduct[]> {
    return await prisma.product.findMany({
      where: {
        id: { in: ids },
        isActive: true
      },
      include: {
        category: true,
        images: {
          take: 1
        }
      }
    });
  }
}
```

### Redis缓存实现
```typescript
// backend/src/utils/cache.ts
import Redis from 'ioredis';

class CacheService {
  private redis: Redis;

  constructor() {
    this.redis = new Redis({
      host: process.env.REDIS_HOST || 'localhost',
      port: parseInt(process.env.REDIS_PORT || '6379'),
      password: process.env.REDIS_PASSWORD,
      db: parseInt(process.env.REDIS_DB || '0'),
      retryDelayOnFailover: 100,
      maxRetriesPerRequest: 3,
      lazyConnect: true
    });

    this.redis.on('error', (error) => {
      console.error('Redis连接错误:', error);
    });
  }

  /**
   * 设置缓存
   */
  async set(key: string, value: any, ttl: number = 300): Promise<void> {
    try {
      const serialized = JSON.stringify(value);
      await this.redis.setex(key, ttl, serialized);
    } catch (error) {
      console.error('设置缓存失败:', error);
    }
  }

  /**
   * 获取缓存
   */
  async get<T>(key: string): Promise<T | null> {
    try {
      const cached = await this.redis.get(key);
      return cached ? JSON.parse(cached) : null;
    } catch (error) {
      console.error('获取缓存失败:', error);
      return null;
    }
  }

  /**
   * 删除缓存
   */
  async del(key: string): Promise<void> {
    try {
      await this.redis.del(key);
    } catch (error) {
      console.error('删除缓存失败:', error);
    }
  }

  /**
   * 批量删除缓存
   */
  async delPattern(pattern: string): Promise<void> {
    try {
      const keys = await this.redis.keys(pattern);
      if (keys.length > 0) {
        await this.redis.del(...keys);
      }
    } catch (error) {
      console.error('批量删除缓存失败:', error);
    }
  }

  /**
   * 缓存装饰器
   */
  withCache<T>(
    keyGenerator: (...args: any[]) => string,
    ttl: number = 300
  ) {
    return (target: any, propertyName: string, descriptor: PropertyDescriptor) => {
      const method = descriptor.value;

      descriptor.value = async function (...args: any[]): Promise<T> {
        const key = keyGenerator(...args);
        
        // 尝试从缓存获取
        const cached = await this.get<T>(key);
        if (cached) {
          return cached;
        }

        // 执行原方法
        const result = await method.apply(this, args);
        
        // 设置缓存
        await this.set(key, result, ttl);
        
        return result;
      };
    };
  }
}

export const cacheService = new CacheService();

// 使用示例
export class ProductController {
  @cacheService.withCache(
    (page: number, limit: number) => `products:list:${page}:${limit}`,
    300 // 5分钟缓存
  )
  async getProducts(page: number, limit: number) {
    // 实际的数据库查询逻辑
  }
}
```

### API响应优化
```typescript
// backend/src/middleware/compression.ts
import compression from 'compression';
import { Request, Response, NextFunction } from 'express';

/**
 * 响应压缩中间件
 */
export const compressionMiddleware = compression({
  level: 6, // 压缩级别
  threshold: 1024, // 只压缩大于1KB的响应
  filter: (req: Request, res: Response) => {
    // 不压缩已经压缩的内容
    if (req.headers['x-no-compression']) {
      return false;
    }
    
    // 使用默认过滤器
    return compression.filter(req, res);
  }
});

// backend/src/middleware/etag.ts
/**
 * ETag缓存中间件
 */
export const etagMiddleware = (req: Request, res: Response, next: NextFunction) => {
  const originalSend = res.send;
  
  res.send = function(data: any) {
    if (req.method === 'GET' && res.statusCode === 200) {
      const etag = `"${Buffer.from(JSON.stringify(data)).toString('base64')}"`;
      res.set('ETag', etag);
      
      if (req.headers['if-none-match'] === etag) {
        res.status(304).end();
        return res;
      }
    }
    
    return originalSend.call(this, data);
  };
  
  next();
};
```

## 数据库性能优化

### 索引优化
```sql
-- 产品表索引优化
CREATE INDEX idx_products_category_active ON products(category_id, is_active);
CREATE INDEX idx_products_slug ON products(slug);
CREATE INDEX idx_products_created_at ON products(created_at DESC);
CREATE INDEX idx_products_search ON products(name, description);

-- 复合索引
CREATE INDEX idx_products_category_active_created ON products(category_id, is_active, created_at DESC);

-- 全文搜索索引
ALTER TABLE products ADD FULLTEXT(name, description);

-- 分类表索引
CREATE INDEX idx_categories_slug ON categories(slug);
CREATE INDEX idx_categories_parent ON categories(parent_id);

-- 用户访问统计索引
CREATE INDEX idx_analytics_date ON analytics(date);
CREATE INDEX idx_analytics_page ON analytics(page_path);
CREATE INDEX idx_analytics_date_page ON analytics(date, page_path);
```

### 查询优化配置
```sql
-- MySQL配置优化
SET GLOBAL innodb_buffer_pool_size = 1073741824; -- 1GB
SET GLOBAL query_cache_size = 268435456; -- 256MB
SET GLOBAL query_cache_type = 1;
SET GLOBAL slow_query_log = 1;
SET GLOBAL long_query_time = 2;
SET GLOBAL max_connections = 200;

-- 连接池配置
SET GLOBAL wait_timeout = 28800;
SET GLOBAL interactive_timeout = 28800;
```

## 监控与分析

### 性能监控
```typescript
// backend/src/middleware/performance.ts
import { Request, Response, NextFunction } from 'express';
import { logger } from '@/utils/logger';

interface PerformanceMetrics {
  method: string;
  url: string;
  duration: number;
  statusCode: number;
  timestamp: Date;
}

/**
 * 性能监控中间件
 */
export const performanceMiddleware = (req: Request, res: Response, next: NextFunction) => {
  const startTime = Date.now();
  
  res.on('finish', () => {
    const duration = Date.now() - startTime;
    
    const metrics: PerformanceMetrics = {
      method: req.method,
      url: req.originalUrl,
      duration,
      statusCode: res.statusCode,
      timestamp: new Date()
    };
    
    // 记录慢查询
    if (duration > 1000) {
      logger.warn('慢请求检测', metrics);
    }
    
    // 记录性能指标
    logger.info('请求性能', metrics);
  });
  
  next();
};
```

### 前端性能监控
```typescript
// composables/usePerformance.ts
export const usePerformance = () => {
  /**
   * 页面加载性能监控
   */
  const trackPageLoad = () => {
    if (typeof window !== 'undefined' && 'performance' in window) {
      const navigation = performance.getEntriesByType('navigation')[0] as PerformanceNavigationTiming;
      
      const metrics = {
        // 页面加载时间
        loadTime: navigation.loadEventEnd - navigation.loadEventStart,
        // DOM解析时间
        domParseTime: navigation.domContentLoadedEventEnd - navigation.domContentLoadedEventStart,
        // 首字节时间
        ttfb: navigation.responseStart - navigation.requestStart,
        // 白屏时间
        fcp: 0,
        // 最大内容绘制
        lcp: 0
      };
      
      // 获取FCP
      const observer = new PerformanceObserver((list) => {
        for (const entry of list.getEntries()) {
          if (entry.name === 'first-contentful-paint') {
            metrics.fcp = entry.startTime;
          }
        }
      });
      observer.observe({ entryTypes: ['paint'] });
      
      // 获取LCP
      const lcpObserver = new PerformanceObserver((list) => {
        const entries = list.getEntries();
        const lastEntry = entries[entries.length - 1];
        metrics.lcp = lastEntry.startTime;
      });
      lcpObserver.observe({ entryTypes: ['largest-contentful-paint'] });
      
      // 发送性能数据
      setTimeout(() => {
        $fetch('/api/analytics/performance', {
          method: 'POST',
          body: {
            url: window.location.pathname,
            metrics,
            userAgent: navigator.userAgent,
            timestamp: new Date().toISOString()
          }
        }).catch(console.error);
      }, 1000);
    }
  };

  /**
   * 资源加载监控
   */
  const trackResourceLoad = () => {
    if (typeof window !== 'undefined') {
      const observer = new PerformanceObserver((list) => {
        for (const entry of list.getEntries()) {
          if (entry.duration > 1000) { // 超过1秒的资源
            console.warn('慢资源加载:', {
              name: entry.name,
              duration: entry.duration,
              size: (entry as any).transferSize
            });
          }
        }
      });
      
      observer.observe({ entryTypes: ['resource'] });
    }
  };

  return {
    trackPageLoad,
    trackResourceLoad
  };
};
```

### 性能优化建议
```typescript
// utils/performanceOptimizer.ts
export class PerformanceOptimizer {
  /**
   * 图片懒加载优化
   */
  static optimizeImages() {
    // 预加载关键图片
    const criticalImages = [
      '/images/hero-banner.webp',
      '/images/logo.webp'
    ];
    
    criticalImages.forEach(src => {
      const link = document.createElement('link');
      link.rel = 'preload';
      link.as = 'image';
      link.href = src;
      document.head.appendChild(link);
    });
  }

  /**
   * 字体优化
   */
  static optimizeFonts() {
    // 预加载字体
    const fonts = [
      '/fonts/inter-var.woff2'
    ];
    
    fonts.forEach(href => {
      const link = document.createElement('link');
      link.rel = 'preload';
      link.as = 'font';
      link.type = 'font/woff2';
      link.crossOrigin = 'anonymous';
      link.href = href;
      document.head.appendChild(link);
    });
  }

  /**
   * 预连接优化
   */
  static preconnectDomains() {
    const domains = [
      'https://api.yourpos.com',
      'https://cdn.yourpos.com'
    ];
    
    domains.forEach(href => {
      const link = document.createElement('link');
      link.rel = 'preconnect';
      link.href = href;
      document.head.appendChild(link);
    });
  }

  /**
   * Service Worker缓存
   */
  static registerServiceWorker() {
    if ('serviceWorker' in navigator) {
      navigator.serviceWorker.register('/sw.js')
        .then(registration => {
          console.log('SW注册成功:', registration);
        })
        .catch(error => {
          console.log('SW注册失败:', error);
        });
    }
  }
}
```

