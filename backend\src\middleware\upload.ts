/**
 * 文件上传中间件
 */

import multer from 'multer';
import path from 'path';
import fs from 'fs';
import { config } from '../config';
import { ValidationError } from './errorHandler';
import { generateRandomString } from '@/shared/utils';

// 确保上传目录存在
if (!fs.existsSync(config.upload.path)) {
  fs.mkdirSync(config.upload.path, { recursive: true });
}

// 确保子目录存在
const imageDir = path.join(config.upload.path, 'images');
const fileDir = path.join(config.upload.path, 'files');

if (!fs.existsSync(imageDir)) {
  fs.mkdirSync(imageDir, { recursive: true });
}

if (!fs.existsSync(fileDir)) {
  fs.mkdirSync(fileDir, { recursive: true });
}

/**
 * 存储配置
 */
const storage = multer.diskStorage({
  destination: (req, file, cb) => {
    // 根据文件类型决定存储目录
    if (config.upload.allowedImageTypes.includes(file.mimetype)) {
      cb(null, imageDir);
    } else {
      cb(null, fileDir);
    }
  },
  filename: (req, file, cb) => {
    // 生成唯一文件名
    const ext = path.extname(file.originalname);
    const name = path.basename(file.originalname, ext);
    const timestamp = Date.now();
    const random = generateRandomString(8);
    const filename = `${name}-${timestamp}-${random}${ext}`;
    cb(null, filename);
  }
});

/**
 * 文件过滤器
 */
const fileFilter = (req: any, file: Express.Multer.File, cb: multer.FileFilterCallback) => {
  // 检查文件类型
  const isImage = config.upload.allowedImageTypes.includes(file.mimetype);
  const isFile = config.upload.allowedFileTypes.includes(file.mimetype);
  
  if (isImage || isFile) {
    cb(null, true);
  } else {
    cb(new ValidationError(`不支持的文件类型: ${file.mimetype}`));
  }
};

/**
 * 上传中间件配置
 */
export const uploadMiddleware = multer({
  storage,
  fileFilter,
  limits: {
    fileSize: config.upload.maxFileSize,
    files: 10, // 最多10个文件
  },
});

/**
 * 图片上传中间件
 */
export const imageUploadMiddleware = multer({
  storage: multer.diskStorage({
    destination: (req, file, cb) => {
      cb(null, imageDir);
    },
    filename: (req, file, cb) => {
      const ext = path.extname(file.originalname);
      const name = path.basename(file.originalname, ext);
      const timestamp = Date.now();
      const random = generateRandomString(8);
      const filename = `${name}-${timestamp}-${random}${ext}`;
      cb(null, filename);
    }
  }),
  fileFilter: (req, file, cb) => {
    if (config.upload.allowedImageTypes.includes(file.mimetype)) {
      cb(null, true);
    } else {
      cb(new ValidationError(`不支持的图片格式: ${file.mimetype}`));
    }
  },
  limits: {
    fileSize: config.upload.maxImageSize,
    files: 10,
  },
});

/**
 * 文件上传中间件
 */
export const fileUploadMiddleware = multer({
  storage: multer.diskStorage({
    destination: (req, file, cb) => {
      cb(null, fileDir);
    },
    filename: (req, file, cb) => {
      const ext = path.extname(file.originalname);
      const name = path.basename(file.originalname, ext);
      const timestamp = Date.now();
      const random = generateRandomString(8);
      const filename = `${name}-${timestamp}-${random}${ext}`;
      cb(null, filename);
    }
  }),
  fileFilter: (req, file, cb) => {
    if (config.upload.allowedFileTypes.includes(file.mimetype)) {
      cb(null, true);
    } else {
      cb(new ValidationError(`不支持的文件格式: ${file.mimetype}`));
    }
  },
  limits: {
    fileSize: config.upload.maxFileSize,
    files: 5,
  },
});

/**
 * 获取文件访问URL
 */
export function getFileUrl(filename: string, isImage: boolean = true): string {
  const baseUrl = config.site.url;
  const subDir = isImage ? 'images' : 'files';
  return `${baseUrl}/uploads/${subDir}/${filename}`;
}

/**
 * 删除文件
 */
export function deleteFile(filename: string, isImage: boolean = true): Promise<void> {
  return new Promise((resolve, reject) => {
    const subDir = isImage ? 'images' : 'files';
    const filePath = path.join(config.upload.path, subDir, filename);
    
    fs.unlink(filePath, (err) => {
      if (err && err.code !== 'ENOENT') {
        reject(err);
      } else {
        resolve();
      }
    });
  });
}

/**
 * 检查文件是否存在
 */
export function fileExists(filename: string, isImage: boolean = true): boolean {
  const subDir = isImage ? 'images' : 'files';
  const filePath = path.join(config.upload.path, subDir, filename);
  return fs.existsSync(filePath);
}

/**
 * 获取文件信息
 */
export function getFileInfo(filename: string, isImage: boolean = true): {
  path: string;
  size: number;
  exists: boolean;
} | null {
  const subDir = isImage ? 'images' : 'files';
  const filePath = path.join(config.upload.path, subDir, filename);
  
  try {
    const stats = fs.statSync(filePath);
    return {
      path: filePath,
      size: stats.size,
      exists: true,
    };
  } catch {
    return {
      path: filePath,
      size: 0,
      exists: false,
    };
  }
}

/**
 * 清理过期文件
 */
export async function cleanupOldFiles(daysToKeep: number = 30): Promise<void> {
  const cutoffDate = new Date();
  cutoffDate.setDate(cutoffDate.getDate() - daysToKeep);
  
  const directories = [imageDir, fileDir];
  
  for (const dir of directories) {
    try {
      const files = fs.readdirSync(dir);
      
      for (const file of files) {
        const filePath = path.join(dir, file);
        const stats = fs.statSync(filePath);
        
        if (stats.mtime < cutoffDate) {
          fs.unlinkSync(filePath);
          console.log(`Deleted old file: ${file}`);
        }
      }
    } catch (error) {
      console.error(`Error cleaning up directory ${dir}:`, error);
    }
  }
}

// 定期清理过期文件（每天执行一次）
if (config.env === 'production') {
  setInterval(() => {
    cleanupOldFiles(30); // 保留30天的文件
  }, 24 * 60 * 60 * 1000); // 24小时
}
