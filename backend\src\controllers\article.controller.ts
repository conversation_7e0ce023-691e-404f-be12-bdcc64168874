/**
 * 文章控制器
 */

import { Request, Response } from 'express';
import { DatabaseManager } from '../config/database';
import { logger } from '../utils/logger';
import { ApiResponse, PaginationHelper } from '../utils/response';
import { NotFoundError, ValidationError } from '../middleware/errorHandler';
import type { IArticleQuery } from '@/shared/types';
import { ArticleStatus } from '@prisma/client';

/**
 * 文章控制器
 */
export class ArticleController {
  private prisma = DatabaseManager.getInstance().getPrisma();

  /**
   * 获取文章列表
   */
  public async getArticles(req: Request, res: Response): Promise<void> {
    const query = req.query as unknown as IArticleQuery;
    
    // 验证和处理分页参数
    const { page, limit } = PaginationHelper.validateParams(
      Number(query.page) || 1,
      Number(query.limit) || 10
    );

    // 构建查询条件
    const where: any = {};

    // 只有管理员可以查看所有状态的文章，普通用户只能看已发布的
    if (!req.user || req.user.role === 'USER') {
      where.status = ArticleStatus.PUBLISHED;
      where.publishedAt = { lte: new Date() };
    } else if (query.status) {
      where.status = query.status;
    }

    // 分类筛选
    if (query.category) {
      where.category = {
        slug: query.category,
      };
    }

    // 搜索关键词
    if (query.search) {
      where.OR = [
        { title: { contains: query.search } },
        { excerpt: { contains: query.search } },
        { content: { contains: query.search } },
      ];
    }

    // 排序
    const orderBy: any = {};
    if (query.sortBy) {
      const sortOrder = query.sortOrder || 'desc';
      switch (query.sortBy) {
        case 'title':
          orderBy.title = sortOrder;
          break;
        case 'created':
          orderBy.createdAt = sortOrder;
          break;
        case 'updated':
          orderBy.updatedAt = sortOrder;
          break;
        case 'published':
          orderBy.publishedAt = sortOrder;
          break;
        default:
          orderBy.createdAt = 'desc';
      }
    } else {
      orderBy.publishedAt = 'desc';
    }

    try {
      // 获取总数
      const total = await this.prisma.article.count({ where });

      // 获取文章列表
      const articles = await this.prisma.article.findMany({
        where,
        include: {
          author: {
            select: {
              id: true,
              name: true,
              avatar: true,
            },
          },
          category: {
            select: {
              id: true,
              name: true,
              slug: true,
            },
          },
          tags: {
            include: {
              tag: {
                select: {
                  id: true,
                  name: true,
                  slug: true,
                  color: true,
                },
              },
            },
          },
        },
        orderBy,
        skip: PaginationHelper.getSkip(page, limit),
        take: limit,
      });

      // 计算分页信息
      const pagination = PaginationHelper.calculate(page, limit, total);

      res.json(ApiResponse.paginated(articles, pagination));
    } catch (error) {
      logger.error('获取文章列表失败', { error, query });
      throw error;
    }
  }

  /**
   * 获取文章详情
   */
  public async getArticle(req: Request, res: Response): Promise<void> {
    const { slug } = req.params;

    try {
      const article = await this.prisma.article.findUnique({
        where: { slug },
        include: {
          author: {
            select: {
              id: true,
              name: true,
              avatar: true,
            },
          },
          category: {
            select: {
              id: true,
              name: true,
              slug: true,
            },
          },
          tags: {
            include: {
              tag: {
                select: {
                  id: true,
                  name: true,
                  slug: true,
                  color: true,
                },
              },
            },
          },
        },
      });

      if (!article) {
        throw new NotFoundError('文章');
      }

      // 检查文章是否可以访问
      if (article.status !== ArticleStatus.PUBLISHED && 
          (!req.user || req.user.role === 'USER')) {
        throw new NotFoundError('文章');
      }

      // 增加浏览次数
      await this.prisma.article.update({
        where: { id: article.id },
        data: { viewCount: { increment: 1 } },
      });

      // 获取相关文章
      const relatedArticles = await this.prisma.article.findMany({
        where: {
          categoryId: article.categoryId,
          id: { not: article.id },
          status: ArticleStatus.PUBLISHED,
          publishedAt: { lte: new Date() },
        },
        include: {
          author: {
            select: {
              name: true,
            },
          },
        },
        take: 5,
        orderBy: { publishedAt: 'desc' },
      });

      res.json(ApiResponse.success({
        ...article,
        relatedArticles,
      }));
    } catch (error) {
      logger.error('获取文章详情失败', { error, slug });
      throw error;
    }
  }

  /**
   * 创建文章（编辑者）
   */
  public async createArticle(req: Request, res: Response): Promise<void> {
    const data = req.body;

    try {
      // 检查slug是否已存在
      const existingArticle = await this.prisma.article.findUnique({
        where: { slug: data.slug },
      });

      if (existingArticle) {
        throw new ValidationError('文章标识符已存在');
      }

      // 如果指定了分类，检查分类是否存在
      if (data.categoryId) {
        const category = await this.prisma.article.findUnique({
          where: { id: data.categoryId },
        });

        if (!category) {
          throw new NotFoundError('文章分类');
        }
      }

      const article = await this.prisma.article.create({
        data: {
          ...data,
          authorId: req.user!.id,
          status: data.status || ArticleStatus.DRAFT,
        },
        include: {
          author: {
            select: {
              id: true,
              name: true,
              avatar: true,
            },
          },
          category: {
            select: {
              id: true,
              name: true,
              slug: true,
            },
          },
        },
      });

      logger.info('文章创建成功', {
        articleId: article.id,
        title: article.title,
        userId: req.user?.id,
      });

      res.status(201).json(ApiResponse.created(article));
    } catch (error) {
      logger.error('创建文章失败', { error, data });
      throw error;
    }
  }

  /**
   * 更新文章（编辑者）
   */
  public async updateArticle(req: Request, res: Response): Promise<void> {
    const { id } = req.params;
    const data = req.body;

    try {
      const articleId = parseInt(id);
      
      // 检查文章是否存在
      const existingArticle = await this.prisma.article.findUnique({
        where: { id: articleId },
      });

      if (!existingArticle) {
        throw new NotFoundError('文章');
      }

      // 检查权限（只有作者或管理员可以编辑）
      if (existingArticle.authorId !== req.user!.id && req.user!.role !== 'ADMIN') {
        throw new ValidationError('只能编辑自己的文章');
      }

      // 如果更新slug，检查是否已存在
      if (data.slug && data.slug !== existingArticle.slug) {
        const slugExists = await this.prisma.article.findUnique({
          where: { slug: data.slug },
        });

        if (slugExists) {
          throw new ValidationError('文章标识符已存在');
        }
      }

      const article = await this.prisma.article.update({
        where: { id: articleId },
        data,
        include: {
          author: {
            select: {
              id: true,
              name: true,
              avatar: true,
            },
          },
          category: {
            select: {
              id: true,
              name: true,
              slug: true,
            },
          },
        },
      });

      logger.info('文章更新成功', {
        articleId: article.id,
        title: article.title,
        userId: req.user?.id,
      });

      res.json(ApiResponse.updated(article));
    } catch (error) {
      logger.error('更新文章失败', { error, id, data });
      throw error;
    }
  }

  /**
   * 发布文章（编辑者）
   */
  public async publishArticle(req: Request, res: Response): Promise<void> {
    const { id } = req.params;

    try {
      const articleId = parseInt(id);
      
      const article = await this.prisma.article.findUnique({
        where: { id: articleId },
      });

      if (!article) {
        throw new NotFoundError('文章');
      }

      // 检查权限
      if (article.authorId !== req.user!.id && req.user!.role !== 'ADMIN') {
        throw new ValidationError('只能发布自己的文章');
      }

      const updatedArticle = await this.prisma.article.update({
        where: { id: articleId },
        data: {
          status: ArticleStatus.PUBLISHED,
          publishedAt: new Date(),
        },
        include: {
          author: {
            select: {
              id: true,
              name: true,
              avatar: true,
            },
          },
        },
      });

      logger.info('文章发布成功', {
        articleId: updatedArticle.id,
        title: updatedArticle.title,
        userId: req.user?.id,
      });

      res.json(ApiResponse.success(updatedArticle, '文章发布成功'));
    } catch (error) {
      logger.error('发布文章失败', { error, id });
      throw error;
    }
  }

  /**
   * 取消发布文章（编辑者）
   */
  public async unpublishArticle(req: Request, res: Response): Promise<void> {
    const { id } = req.params;

    try {
      const articleId = parseInt(id);
      
      const article = await this.prisma.article.findUnique({
        where: { id: articleId },
      });

      if (!article) {
        throw new NotFoundError('文章');
      }

      // 检查权限
      if (article.authorId !== req.user!.id && req.user!.role !== 'ADMIN') {
        throw new ValidationError('只能操作自己的文章');
      }

      const updatedArticle = await this.prisma.article.update({
        where: { id: articleId },
        data: {
          status: ArticleStatus.DRAFT,
          publishedAt: null,
        },
        include: {
          author: {
            select: {
              id: true,
              name: true,
              avatar: true,
            },
          },
        },
      });

      logger.info('文章取消发布成功', {
        articleId: updatedArticle.id,
        title: updatedArticle.title,
        userId: req.user?.id,
      });

      res.json(ApiResponse.success(updatedArticle, '文章已取消发布'));
    } catch (error) {
      logger.error('取消发布文章失败', { error, id });
      throw error;
    }
  }

  /**
   * 删除文章（编辑者）
   */
  public async deleteArticle(req: Request, res: Response): Promise<void> {
    const { id } = req.params;

    try {
      const articleId = parseInt(id);
      
      const article = await this.prisma.article.findUnique({
        where: { id: articleId },
      });

      if (!article) {
        throw new NotFoundError('文章');
      }

      // 检查权限
      if (article.authorId !== req.user!.id && req.user!.role !== 'ADMIN') {
        throw new ValidationError('只能删除自己的文章');
      }

      await this.prisma.article.delete({
        where: { id: articleId },
      });

      logger.info('文章删除成功', {
        articleId,
        title: article.title,
        userId: req.user?.id,
      });

      res.json(ApiResponse.deleted());
    } catch (error) {
      logger.error('删除文章失败', { error, id });
      throw error;
    }
  }
}
