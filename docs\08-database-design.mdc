---
description: 
globs: 
alwaysApply: true
---
# 数据库设计规范

## Prisma Schema 设计

### 完整数据库模型
```prisma
// prisma/schema.prisma
generator client {
  provider = "prisma-client-js"
}

datasource db {
  provider = "mysql"
  url      = env("DATABASE_URL")
}

// 用户表
model User {
  id          Int      @id @default(autoincrement())
  email       String   @unique @db.VarChar(255)
  password    String   @db.VarChar(255)
  name        String   @db.VarChar(100)
  avatar      String?  @db.VarChar(500)
  role        UserRole @default(USER)
  isActive    Boolean  @default(true) @map("is_active")
  lastLoginAt DateTime? @map("last_login_at")
  createdAt   DateTime @default(now()) @map("created_at")
  updatedAt   DateTime @updatedAt @map("updated_at")

  // 关联关系
  sessions    UserSession[]
  reviews     ProductReview[]
  inquiries   ContactInquiry[]

  @@map("users")
}

// 用户会话表
model UserSession {
  id           String   @id @default(cuid())
  userId       Int      @map("user_id")
  refreshToken String   @unique @db.VarChar(500) @map("refresh_token")
  userAgent    String?  @db.Text @map("user_agent")
  ipAddress    String?  @db.VarChar(45) @map("ip_address")
  expiresAt    DateTime @map("expires_at")
  createdAt    DateTime @default(now()) @map("created_at")

  // 关联关系
  user User @relation(fields: [userId], references: [id], onDelete: Cascade)

  @@map("user_sessions")
}

// 分类表
model Category {
  id          Int      @id @default(autoincrement())
  name        String   @db.VarChar(100)
  slug        String   @unique @db.VarChar(100)
  description String?  @db.Text
  image       String?  @db.VarChar(500)
  parentId    Int?     @map("parent_id")
  sortOrder   Int      @default(0) @map("sort_order")
  isActive    Boolean  @default(true) @map("is_active")
  createdAt   DateTime @default(now()) @map("created_at")
  updatedAt   DateTime @updatedAt @map("updated_at")

  // 关联关系
  parent   Category?  @relation("CategoryHierarchy", fields: [parentId], references: [id])
  children Category[] @relation("CategoryHierarchy")
  products Product[]

  @@map("categories")
}

// 产品表
model Product {
  id          Int      @id @default(autoincrement())
  name        String   @db.VarChar(255)
  slug        String   @unique @db.VarChar(255)
  description String?  @db.Text
  shortDesc   String?  @db.VarChar(500) @map("short_desc")
  sku         String?  @unique @db.VarChar(100)
  price       Decimal  @db.Decimal(10, 2)
  originalPrice Decimal? @db.Decimal(10, 2) @map("original_price")
  categoryId  Int      @map("category_id")
  brand       String?  @db.VarChar(100)
  model       String?  @db.VarChar(100)
  weight      Float?
  dimensions  String?  @db.VarChar(100)
  color       String?  @db.VarChar(50)
  material    String?  @db.VarChar(100)
  warranty    String?  @db.VarChar(200)
  stock       Int      @default(0)
  minStock    Int      @default(0) @map("min_stock")
  maxStock    Int      @default(999999) @map("max_stock")
  isActive    Boolean  @default(true) @map("is_active")
  isFeatured  Boolean  @default(false) @map("is_featured")
  isNew       Boolean  @default(false) @map("is_new")
  viewCount   Int      @default(0) @map("view_count")
  seoTitle    String?  @db.VarChar(255) @map("seo_title")
  seoDesc     String?  @db.VarChar(500) @map("seo_desc")
  seoKeywords String?  @db.VarChar(500) @map("seo_keywords")
  createdAt   DateTime @default(now()) @map("created_at")
  updatedAt   DateTime @updatedAt @map("updated_at")

  // 关联关系
  category      Category           @relation(fields: [categoryId], references: [id])
  images        ProductImage[]
  specifications ProductSpecification[]
  reviews       ProductReview[]
  tags          ProductTag[]

  @@map("products")
}

// 产品图片表
model ProductImage {
  id        Int     @id @default(autoincrement())
  productId Int     @map("product_id")
  url       String  @db.VarChar(500)
  alt       String? @db.VarChar(255)
  title     String? @db.VarChar(255)
  sortOrder Int     @default(0) @map("sort_order")
  isPrimary Boolean @default(false) @map("is_primary")
  createdAt DateTime @default(now()) @map("created_at")

  // 关联关系
  product Product @relation(fields: [productId], references: [id], onDelete: Cascade)

  @@map("product_images")
}

// 产品规格表
model ProductSpecification {
  id        Int    @id @default(autoincrement())
  productId Int    @map("product_id")
  name      String @db.VarChar(100)
  value     String @db.VarChar(500)
  unit      String? @db.VarChar(20)
  sortOrder Int    @default(0) @map("sort_order")

  // 关联关系
  product Product @relation(fields: [productId], references: [id], onDelete: Cascade)

  @@map("product_specifications")
}

// 产品评价表
model ProductReview {
  id        Int      @id @default(autoincrement())
  productId Int      @map("product_id")
  userId    Int?     @map("user_id")
  name      String   @db.VarChar(100)
  email     String   @db.VarChar(255)
  rating    Int      @db.TinyInt
  title     String?  @db.VarChar(255)
  content   String   @db.Text
  isApproved Boolean @default(false) @map("is_approved")
  createdAt DateTime @default(now()) @map("created_at")
  updatedAt DateTime @updatedAt @map("updated_at")

  // 关联关系
  product Product @relation(fields: [productId], references: [id], onDelete: Cascade)
  user    User?   @relation(fields: [userId], references: [id], onDelete: SetNull)

  @@map("product_reviews")
}

// 产品标签表
model Tag {
  id        Int      @id @default(autoincrement())
  name      String   @unique @db.VarChar(50)
  slug      String   @unique @db.VarChar(50)
  color     String?  @db.VarChar(7)
  createdAt DateTime @default(now()) @map("created_at")

  // 关联关系
  products ProductTag[]

  @@map("tags")
}

// 产品标签关联表
model ProductTag {
  productId Int @map("product_id")
  tagId     Int @map("tag_id")

  // 关联关系
  product Product @relation(fields: [productId], references: [id], onDelete: Cascade)
  tag     Tag     @relation(fields: [tagId], references: [id], onDelete: Cascade)

  @@id([productId, tagId])
  @@map("product_tags")
}

// 公司信息表
model CompanyInfo {
  id          Int      @id @default(autoincrement())
  name        String   @db.VarChar(255)
  description String?  @db.Text
  logo        String?  @db.VarChar(500)
  address     String?  @db.VarChar(500)
  phone       String?  @db.VarChar(50)
  email       String?  @db.VarChar(255)
  website     String?  @db.VarChar(255)
  founded     Int?
  employees   String?  @db.VarChar(50)
  revenue     String?  @db.VarChar(50)
  mission     String?  @db.Text
  vision      String?  @db.Text
  values      String?  @db.Text
  createdAt   DateTime @default(now()) @map("created_at")
  updatedAt   DateTime @updatedAt @map("updated_at")

  @@map("company_info")
}

// 新闻文章表
model Article {
  id          Int           @id @default(autoincrement())
  title       String        @db.VarChar(255)
  slug        String        @unique @db.VarChar(255)
  excerpt     String?       @db.VarChar(500)
  content     String        @db.LongText
  featuredImage String?     @db.VarChar(500) @map("featured_image")
  authorId    Int?          @map("author_id")
  categoryId  Int?          @map("category_id")
  status      ArticleStatus @default(DRAFT)
  isSticky    Boolean       @default(false) @map("is_sticky")
  viewCount   Int           @default(0) @map("view_count")
  seoTitle    String?       @db.VarChar(255) @map("seo_title")
  seoDesc     String?       @db.VarChar(500) @map("seo_desc")
  seoKeywords String?       @db.VarChar(500) @map("seo_keywords")
  publishedAt DateTime?     @map("published_at")
  createdAt   DateTime      @default(now()) @map("created_at")
  updatedAt   DateTime      @updatedAt @map("updated_at")

  // 关联关系
  author   User?            @relation(fields: [authorId], references: [id], onDelete: SetNull)
  category ArticleCategory? @relation(fields: [categoryId], references: [id], onDelete: SetNull)
  tags     ArticleTag[]

  @@map("articles")
}

// 文章分类表
model ArticleCategory {
  id          Int      @id @default(autoincrement())
  name        String   @db.VarChar(100)
  slug        String   @unique @db.VarChar(100)
  description String?  @db.Text
  sortOrder   Int      @default(0) @map("sort_order")
  createdAt   DateTime @default(now()) @map("created_at")
  updatedAt   DateTime @updatedAt @map("updated_at")

  // 关联关系
  articles Article[]

  @@map("article_categories")
}

// 文章标签关联表
model ArticleTag {
  articleId Int @map("article_id")
  tagId     Int @map("tag_id")

  // 关联关系
  article Article @relation(fields: [articleId], references: [id], onDelete: Cascade)
  tag     Tag     @relation(fields: [tagId], references: [id], onDelete: Cascade)

  @@id([articleId, tagId])
  @@map("article_tags")
}

// 联系咨询表
model ContactInquiry {
  id        Int           @id @default(autoincrement())
  name      String        @db.VarChar(100)
  email     String        @db.VarChar(255)
  phone     String?       @db.VarChar(50)
  company   String?       @db.VarChar(255)
  subject   String        @db.VarChar(255)
  message   String        @db.Text
  status    InquiryStatus @default(PENDING)
  userId    Int?          @map("user_id")
  assignedTo Int?         @map("assigned_to")
  repliedAt DateTime?     @map("replied_at")
  createdAt DateTime      @default(now()) @map("created_at")
  updatedAt DateTime      @updatedAt @map("updated_at")

  // 关联关系
  user     User? @relation(fields: [userId], references: [id], onDelete: SetNull)
  assignee User? @relation("AssignedInquiries", fields: [assignedTo], references: [id], onDelete: SetNull)

  @@map("contact_inquiries")
}

// 网站设置表
model Setting {
  id        Int      @id @default(autoincrement())
  key       String   @unique @db.VarChar(100)
  value     String?  @db.Text
  type      String   @db.VarChar(50) @default("string")
  group     String   @db.VarChar(50) @default("general")
  label     String   @db.VarChar(255)
  description String? @db.Text
  sortOrder Int      @default(0) @map("sort_order")
  createdAt DateTime @default(now()) @map("created_at")
  updatedAt DateTime @updatedAt @map("updated_at")

  @@map("settings")
}

// 访问统计表
model Analytics {
  id         Int      @id @default(autoincrement())
  date       DateTime @db.Date
  pagePath   String   @db.VarChar(500) @map("page_path")
  pageTitle  String?  @db.VarChar(255) @map("page_title")
  visitors   Int      @default(0)
  pageViews  Int      @default(0) @map("page_views")
  bounceRate Float?   @db.Float @map("bounce_rate")
  avgDuration Float?  @db.Float @map("avg_duration")
  referrer   String?  @db.VarChar(500)
  userAgent  String?  @db.Text @map("user_agent")
  ipAddress  String?  @db.VarChar(45) @map("ip_address")
  country    String?  @db.VarChar(100)
  city       String?  @db.VarChar(100)
  device     String?  @db.VarChar(50)
  browser    String?  @db.VarChar(50)
  os         String?  @db.VarChar(50)
  createdAt  DateTime @default(now()) @map("created_at")

  @@unique([date, pagePath])
  @@map("analytics")
}

// 系统日志表
model SystemLog {
  id        Int       @id @default(autoincrement())
  level     LogLevel
  message   String    @db.Text
  context   Json?
  userId    Int?      @map("user_id")
  ipAddress String?   @db.VarChar(45) @map("ip_address")
  userAgent String?   @db.Text @map("user_agent")
  createdAt DateTime  @default(now()) @map("created_at")

  @@map("system_logs")
}

// 枚举定义
enum UserRole {
  ADMIN
  EDITOR
  USER
}

enum ArticleStatus {
  DRAFT
  PUBLISHED
  ARCHIVED
}

enum InquiryStatus {
  PENDING
  IN_PROGRESS
  RESOLVED
  CLOSED
}

enum LogLevel {
  ERROR
  WARN
  INFO
  DEBUG
}
```

## 数据库迁移管理

### 初始化迁移
```sql
-- 001_initial_migration.sql
-- 创建数据库
CREATE DATABASE IF NOT EXISTS pos_website 
CHARACTER SET utf8mb4 
COLLATE utf8mb4_unicode_ci;

USE pos_website;

-- 用户表
CREATE TABLE users (
  id INT AUTO_INCREMENT PRIMARY KEY,
  email VARCHAR(255) NOT NULL UNIQUE,
  password VARCHAR(255) NOT NULL,
  name VARCHAR(100) NOT NULL,
  avatar VARCHAR(500),
  role ENUM('ADMIN', 'EDITOR', 'USER') DEFAULT 'USER',
  is_active BOOLEAN DEFAULT TRUE,
  last_login_at TIMESTAMP NULL,
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  
  INDEX idx_email (email),
  INDEX idx_role (role),
  INDEX idx_active (is_active)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- 用户会话表
CREATE TABLE user_sessions (
  id VARCHAR(30) PRIMARY KEY,
  user_id INT NOT NULL,
  refresh_token VARCHAR(500) NOT NULL UNIQUE,
  user_agent TEXT,
  ip_address VARCHAR(45),
  expires_at TIMESTAMP NOT NULL,
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  
  FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
  INDEX idx_user_id (user_id),
  INDEX idx_expires_at (expires_at)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- 分类表
CREATE TABLE categories (
  id INT AUTO_INCREMENT PRIMARY KEY,
  name VARCHAR(100) NOT NULL,
  slug VARCHAR(100) NOT NULL UNIQUE,
  description TEXT,
  image VARCHAR(500),
  parent_id INT,
  sort_order INT DEFAULT 0,
  is_active BOOLEAN DEFAULT TRUE,
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  
  FOREIGN KEY (parent_id) REFERENCES categories(id) ON DELETE SET NULL,
  INDEX idx_slug (slug),
  INDEX idx_parent (parent_id),
  INDEX idx_active (is_active),
  INDEX idx_sort (sort_order)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- 产品表
CREATE TABLE products (
  id INT AUTO_INCREMENT PRIMARY KEY,
  name VARCHAR(255) NOT NULL,
  slug VARCHAR(255) NOT NULL UNIQUE,
  description TEXT,
  short_desc VARCHAR(500),
  sku VARCHAR(100) UNIQUE,
  price DECIMAL(10,2) NOT NULL,
  original_price DECIMAL(10,2),
  category_id INT NOT NULL,
  brand VARCHAR(100),
  model VARCHAR(100),
  weight FLOAT,
  dimensions VARCHAR(100),
  color VARCHAR(50),
  material VARCHAR(100),
  warranty VARCHAR(200),
  stock INT DEFAULT 0,
  min_stock INT DEFAULT 0,
  max_stock INT DEFAULT 999999,
  is_active BOOLEAN DEFAULT TRUE,
  is_featured BOOLEAN DEFAULT FALSE,
  is_new BOOLEAN DEFAULT FALSE,
  view_count INT DEFAULT 0,
  seo_title VARCHAR(255),
  seo_desc VARCHAR(500),
  seo_keywords VARCHAR(500),
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  
  FOREIGN KEY (category_id) REFERENCES categories(id),
  INDEX idx_slug (slug),
  INDEX idx_sku (sku),
  INDEX idx_category (category_id),
  INDEX idx_active (is_active),
  INDEX idx_featured (is_featured),
  INDEX idx_price (price),
  INDEX idx_created (created_at),
  FULLTEXT idx_search (name, description)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
```

### 数据种子文件
```typescript
// prisma/seed.ts
import { PrismaClient, UserRole } from '@prisma/client';
import bcrypt from 'bcrypt';

const prisma = new PrismaClient();

async function main() {
  console.log('开始数据库种子数据填充...');

  // 创建管理员用户
  const adminPassword = await bcrypt.hash('admin123456', 12);
  const admin = await prisma.user.upsert({
    where: { email: '<EMAIL>' },
    update: {},
    create: {
      email: '<EMAIL>',
      password: adminPassword,
      name: '系统管理员',
      role: UserRole.ADMIN
    }
  });

  console.log('✓ 管理员用户创建完成');

  // 创建产品分类
  const categories = [
    {
      name: '移动POS机',
      slug: 'mobile-pos',
      description: '便携式移动支付终端设备'
    },
    {
      name: '智能POS机',
      slug: 'smart-pos',
      description: '多功能智能支付终端'
    },
    {
      name: '传统POS机',
      slug: 'traditional-pos',
      description: '经典款台式POS机'
    },
    {
      name: '无线POS机',
      slug: 'wireless-pos',
      description: '无线连接POS终端'
    }
  ];

  for (const categoryData of categories) {
    await prisma.category.upsert({
      where: { slug: categoryData.slug },
      update: {},
      create: categoryData
    });
  }

  console.log('✓ 产品分类创建完成');

  // 创建示例产品
  const mobileCategory = await prisma.category.findUnique({
    where: { slug: 'mobile-pos' }
  });

  if (mobileCategory) {
    const products = [
      {
        name: 'POS-M100 移动支付终端',
        slug: 'pos-m100-mobile-terminal',
        description: '轻便小巧的移动POS机，支持多种支付方式，电池续航长达8小时。',
        shortDesc: '轻便小巧，续航持久的移动支付解决方案',
        sku: 'POS-M100',
        price: 299.00,
        originalPrice: 399.00,
        categoryId: mobileCategory.id,
        brand: 'YourPOS',
        model: 'M100',
        weight: 0.3,
        dimensions: '120x70x20mm',
        color: '黑色',
        material: 'ABS塑料',
        warranty: '1年质保',
        stock: 100,
        isFeatured: true,
        isNew: true
      },
      {
        name: 'POS-M200 4G移动POS',
        slug: 'pos-m200-4g-mobile',
        description: '支持4G网络的移动POS机，内置打印机，适合外卖配送等场景。',
        shortDesc: '4G网络，内置打印，移动支付首选',
        sku: 'POS-M200',
        price: 599.00,
        categoryId: mobileCategory.id,
        brand: 'YourPOS',
        model: 'M200',
        weight: 0.5,
        dimensions: '150x80x25mm',
        color: '白色',
        material: 'ABS塑料',
        warranty: '2年质保',
        stock: 50,
        isFeatured: true
      }
    ];

    for (const productData of products) {
      await prisma.product.upsert({
        where: { slug: productData.slug },
        update: {},
        create: productData
      });
    }
  }

  console.log('✓ 示例产品创建完成');

  // 创建公司信息
  await prisma.companyInfo.upsert({
    where: { id: 1 },
    update: {},
    create: {
      name: 'POS机产品展示公司',
      description: '专业的POS机产品供应商，致力于为商户提供优质的支付解决方案。',
      address: '北京市朝阳区科技园区创新大厦A座1001室',
      phone: '************',
      email: '<EMAIL>',
      website: 'https://www.yourpos.com',
      founded: 2015,
      employees: '50-100人',
      mission: '让支付更简单，让商业更高效',
      vision: '成为国内领先的支付终端设备供应商',
      values: '创新、专业、诚信、服务'
    }
  });

  console.log('✓ 公司信息创建完成');

  // 创建系统设置
  const settings = [
    {
      key: 'site_name',
      value: 'POS机产品展示网站',
      type: 'string',
      group: 'general',
      label: '网站名称',
      description: '网站的显示名称'
    },
    {
      key: 'site_description',
      value: '专业的POS机产品展示平台，提供最新的支付终端设备信息',
      type: 'text',
      group: 'general',
      label: '网站描述',
      description: '网站的SEO描述'
    },
    {
      key: 'contact_email',
      value: '<EMAIL>',
      type: 'email',
      group: 'contact',
      label: '联系邮箱',
      description: '客户联系邮箱'
    },
    {
      key: 'contact_phone',
      value: '************',
      type: 'string',
      group: 'contact',
      label: '联系电话',
      description: '客户服务电话'
    }
  ];

  for (const setting of settings) {
    await prisma.setting.upsert({
      where: { key: setting.key },
      update: {},
      create: setting
    });
  }

  console.log('✓ 系统设置创建完成');

  console.log('数据库种子数据填充完成！');
}

main()
  .catch((e) => {
    console.error(e);
    process.exit(1);
  })
  .finally(async () => {
    await prisma.$disconnect();
  });
```

## 数据库优化策略

### 索引优化
```sql
-- 性能优化索引
-- 产品搜索优化
CREATE INDEX idx_products_search_active ON products(is_active, name, category_id);
CREATE INDEX idx_products_featured_active ON products(is_featured, is_active, created_at DESC);
CREATE INDEX idx_products_category_price ON products(category_id, is_active, price);

-- 分页查询优化
CREATE INDEX idx_products_pagination ON products(is_active, created_at DESC, id);

-- 用户查询优化
CREATE INDEX idx_users_role_active ON users(role, is_active);
CREATE INDEX idx_user_sessions_user_expires ON user_sessions(user_id, expires_at);

-- 分析统计优化
CREATE INDEX idx_analytics_date_path ON analytics(date, page_path);
CREATE INDEX idx_analytics_date_visitors ON analytics(date, visitors DESC);

-- 日志查询优化
CREATE INDEX idx_system_logs_level_created ON system_logs(level, created_at DESC);
CREATE INDEX idx_system_logs_user_created ON system_logs(user_id, created_at DESC);
```

### 查询优化工具
```typescript
// backend/src/utils/queryOptimizer.ts
import { PrismaClient } from '@prisma/client';

export class QueryOptimizer {
  private prisma: PrismaClient;

  constructor(prisma: PrismaClient) {
    this.prisma = prisma;
  }

  /**
   * 优化的产品列表查询
   */
  async getOptimizedProducts(params: {
    page: number;
    limit: number;
    categoryId?: number;
    search?: string;
    sortBy?: string;
    sortOrder?: 'asc' | 'desc';
    featured?: boolean;
  }) {
    const { page, limit, categoryId, search, sortBy = 'createdAt', sortOrder = 'desc', featured } = params;
    const skip = (page - 1) * limit;

    // 构建基础查询条件
    const baseWhere: any = {
      isActive: true
    };

    if (categoryId) {
      baseWhere.categoryId = categoryId;
    }

    if (featured !== undefined) {
      baseWhere.isFeatured = featured;
    }

    if (search) {
      baseWhere.OR = [
        {
          name: {
            contains: search,
            mode: 'insensitive'
          }
        },
        {
          description: {
            contains: search,
            mode: 'insensitive'
          }
        },
        {
          sku: {
            contains: search,
            mode: 'insensitive'
          }
        }
      ];
    }

    // 使用事务确保一致性
    const [products, total] = await this.prisma.$transaction([
      this.prisma.product.findMany({
        where: baseWhere,
        skip,
        take: limit,
        select: {
          id: true,
          name: true,
          slug: true,
          shortDesc: true,
          price: true,
          originalPrice: true,
          sku: true,
          isFeatured: true,
          isNew: true,
          viewCount: true,
          createdAt: true,
          category: {
            select: {
              id: true,
              name: true,
              slug: true
            }
          },
          images: {
            select: {
              id: true,
              url: true,
              alt: true,
              isPrimary: true
            },
            where: {
              isPrimary: true
            },
            take: 1
          },
          _count: {
            select: {
              reviews: true
            }
          }
        },
        orderBy: {
          [sortBy]: sortOrder
        }
      }),
      this.prisma.product.count({
        where: baseWhere
      })
    ]);

    return {
      products,
      pagination: {
        page,
        limit,
        total,
        totalPages: Math.ceil(total / limit),
        hasNext: page * limit < total,
        hasPrev: page > 1
      }
    };
  }

  /**
   * 批量更新产品浏览量
   */
  async batchUpdateViewCount(productIds: number[]) {
    // 使用原生SQL批量更新，性能更好
    await this.prisma.$executeRaw`
      UPDATE products 
      SET view_count = view_count + 1 
      WHERE id IN (${productIds.join(',')})
    `;
  }

  /**
   * 获取热门产品
   */
  async getPopularProducts(limit: number = 10) {
    return await this.prisma.product.findMany({
      where: {
        isActive: true
      },
      select: {
        id: true,
        name: true,
        slug: true,
        price: true,
        viewCount: true,
        images: {
          select: {
            url: true,
            alt: true
          },
          where: {
            isPrimary: true
          },
          take: 1
        }
      },
      orderBy: {
        viewCount: 'desc'
      },
      take: limit
    });
  }

  /**
   * 获取相关产品
   */
  async getRelatedProducts(productId: number, categoryId: number, limit: number = 6) {
    return await this.prisma.product.findMany({
      where: {
        id: {
          not: productId
        },
        categoryId,
        isActive: true
      },
      select: {
        id: true,
        name: true,
        slug: true,
        price: true,
        originalPrice: true,
        images: {
          select: {
            url: true,
            alt: true
          },
          where: {
            isPrimary: true
          },
          take: 1
        }
      },
      take: limit,
      orderBy: {
        viewCount: 'desc'
      }
    });
  }
}
```

### 数据库连接池配置
```typescript
// backend/src/config/database.ts
import { PrismaClient } from '@prisma/client';

/**
 * 数据库连接配置
 */
const databaseConfig = {
  // 连接池配置
  datasources: {
    db: {
      url: process.env.DATABASE_URL
    }
  },
  
  // 日志配置
  log: process.env.NODE_ENV === 'development' 
    ? ['query', 'info', 'warn', 'error'] as const
    : ['error'] as const,
    
  // 错误格式
  errorFormat: 'pretty' as const,
  
  // 连接超时
  __internal: {
    engine: {
      connectTimeout: 60000,
      queryTimeout: 60000
    }
  }
};

/**
 * 创建Prisma客户端实例
 */
export const prisma = new PrismaClient(databaseConfig);

/**
 * 数据库连接管理
 */
export class DatabaseManager {
  private static instance: DatabaseManager;
  private prisma: PrismaClient;
  private isConnected: boolean = false;

  private constructor() {
    this.prisma = prisma;
  }

  public static getInstance(): DatabaseManager {
    if (!DatabaseManager.instance) {
      DatabaseManager.instance = new DatabaseManager();
    }
    return DatabaseManager.instance;
  }

  /**
   * 连接数据库
   */
  async connect(): Promise<void> {
    try {
      await this.prisma.$connect();
      this.isConnected = true;
      console.log('✓ 数据库连接成功');
    } catch (error) {
      console.error('✗ 数据库连接失败:', error);
      throw error;
    }
  }

  /**
   * 断开数据库连接
   */
  async disconnect(): Promise<void> {
    try {
      await this.prisma.$disconnect();
      this.isConnected = false;
      console.log('✓ 数据库连接已断开');
    } catch (error) {
      console.error('✗ 数据库断开连接失败:', error);
      throw error;
    }
  }

  /**
   * 检查数据库连接状态
   */
  async checkConnection(): Promise<boolean> {
    try {
      await this.prisma.$queryRaw`SELECT 1`;
      return true;
    } catch {
      return false;
    }
  }

  /**
   * 获取Prisma客户端
   */
  getPrisma(): PrismaClient {
    return this.prisma;
  }

  /**
   * 健康检查
   */
  async healthCheck(): Promise<{
    status: 'healthy' | 'unhealthy';
    details: {
      connected: boolean;
      responseTime: number;
    };
  }> {
    const startTime = Date.now();
    
    try {
      const isConnected = await this.checkConnection();
      const responseTime = Date.now() - startTime;
      
      return {
        status: isConnected ? 'healthy' : 'unhealthy',
        details: {
          connected: isConnected,
          responseTime
        }
      };
    } catch (error) {
      return {
        status: 'unhealthy',
        details: {
          connected: false,
          responseTime: Date.now() - startTime
        }
      };
    }
  }
}

// 优雅关闭处理
process.on('beforeExit', async () => {
  const dbManager = DatabaseManager.getInstance();
  await dbManager.disconnect();
});

process.on('SIGINT', async () => {
  const dbManager = DatabaseManager.getInstance();
  await dbManager.disconnect();
  process.exit(0);
});

process.on('SIGTERM', async () => {
  const dbManager = DatabaseManager.getInstance();
  await dbManager.disconnect();
  process.exit(0);
});

