/**
 * 邮件服务
 */

import nodemailer from 'nodemailer';
import { config } from '../config';
import { logger } from '../utils/logger';

/**
 * 邮件模板接口
 */
interface EmailTemplate {
  subject: string;
  html: string;
  text?: string;
}

/**
 * 邮件服务类
 */
export class EmailService {
  private transporter: nodemailer.Transporter;

  constructor() {
    this.transporter = nodemailer.createTransporter({
      host: config.email.smtp.host,
      port: config.email.smtp.port,
      secure: config.email.smtp.secure,
      auth: {
        user: config.email.smtp.auth.user,
        pass: config.email.smtp.auth.pass,
      },
    });
  }

  /**
   * 发送邮件
   */
  private async sendEmail(
    to: string,
    subject: string,
    html: string,
    text?: string
  ): Promise<void> {
    try {
      const mailOptions = {
        from: `${config.email.from.name} <${config.email.from.email}>`,
        to,
        subject,
        html,
        text,
      };

      const result = await this.transporter.sendMail(mailOptions);

      logger.info('邮件发送成功', {
        to,
        subject,
        messageId: result.messageId,
      });
    } catch (error) {
      logger.error('邮件发送失败', {
        to,
        subject,
        error: error instanceof Error ? error.message : '未知错误',
      });
      throw error;
    }
  }

  /**
   * 发送欢迎邮件
   */
  async sendWelcomeEmail(email: string, name: string): Promise<void> {
    const template = this.getWelcomeTemplate(name);
    await this.sendEmail(email, template.subject, template.html, template.text);
  }

  /**
   * 发送密码重置邮件
   */
  async sendPasswordResetEmail(email: string, name: string, resetToken: string): Promise<void> {
    const template = this.getPasswordResetTemplate(name, resetToken);
    await this.sendEmail(email, template.subject, template.html, template.text);
  }

  /**
   * 发送联系咨询回复邮件
   */
  async sendContactReplyEmail(
    email: string,
    name: string,
    originalMessage: string,
    reply: string
  ): Promise<void> {
    const template = this.getContactReplyTemplate(name, originalMessage, reply);
    await this.sendEmail(email, template.subject, template.html, template.text);
  }

  /**
   * 发送订单确认邮件
   */
  async sendOrderConfirmationEmail(
    email: string,
    name: string,
    orderDetails: any
  ): Promise<void> {
    const template = this.getOrderConfirmationTemplate(name, orderDetails);
    await this.sendEmail(email, template.subject, template.html, template.text);
  }

  /**
   * 发送联系确认邮件
   */
  async sendContactConfirmationEmail(
    email: string,
    name: string,
    subject: string
  ): Promise<void> {
    const template = this.getContactConfirmationTemplate(name, subject);
    await this.sendEmail(email, template.subject, template.html, template.text);
  }

  /**
   * 发送新咨询通知邮件给管理员
   */
  async sendNewInquiryNotification(inquiry: any): Promise<void> {
    const template = this.getNewInquiryNotificationTemplate(inquiry);
    // 这里应该发送给管理员邮箱，可以从配置中获取
    const adminEmail = config.email.from.email; // 临时使用发送邮箱
    await this.sendEmail(adminEmail, template.subject, template.html, template.text);
  }

  /**
   * 获取欢迎邮件模板
   */
  private getWelcomeTemplate(name: string): EmailTemplate {
    return {
      subject: `欢迎加入${config.site.name}！`,
      html: `
        <!DOCTYPE html>
        <html>
        <head>
          <meta charset="utf-8">
          <title>欢迎加入${config.site.name}</title>
          <style>
            body { font-family: Arial, sans-serif; line-height: 1.6; color: #333; }
            .container { max-width: 600px; margin: 0 auto; padding: 20px; }
            .header { background: #007bff; color: white; padding: 20px; text-align: center; }
            .content { padding: 20px; background: #f9f9f9; }
            .footer { padding: 20px; text-align: center; color: #666; }
            .btn { display: inline-block; padding: 10px 20px; background: #007bff; color: white; text-decoration: none; border-radius: 5px; }
          </style>
        </head>
        <body>
          <div class="container">
            <div class="header">
              <h1>欢迎加入${config.site.name}！</h1>
            </div>
            <div class="content">
              <h2>亲爱的 ${name}，</h2>
              <p>感谢您注册${config.site.name}！我们很高兴您加入我们的社区。</p>
              <p>在这里，您可以：</p>
              <ul>
                <li>浏览最新的POS机产品信息</li>
                <li>查看详细的产品规格和价格</li>
                <li>获取专业的产品咨询服务</li>
                <li>享受优质的售后支持</li>
              </ul>
              <p>如果您有任何问题，请随时联系我们的客服团队。</p>
              <p style="text-align: center;">
                <a href="${config.site.url}" class="btn">立即访问网站</a>
              </p>
            </div>
            <div class="footer">
              <p>此邮件由系统自动发送，请勿回复。</p>
              <p>${config.site.name} | ${config.site.url}</p>
            </div>
          </div>
        </body>
        </html>
      `,
      text: `
        欢迎加入${config.site.name}！

        亲爱的 ${name}，

        感谢您注册${config.site.name}！我们很高兴您加入我们的社区。

        在这里，您可以：
        - 浏览最新的POS机产品信息
        - 查看详细的产品规格和价格
        - 获取专业的产品咨询服务
        - 享受优质的售后支持

        如果您有任何问题，请随时联系我们的客服团队。

        立即访问网站：${config.site.url}

        此邮件由系统自动发送，请勿回复。
        ${config.site.name} | ${config.site.url}
      `,
    };
  }

  /**
   * 获取密码重置邮件模板
   */
  private getPasswordResetTemplate(name: string, resetToken: string): EmailTemplate {
    const resetUrl = `${config.site.url}/reset-password?token=${resetToken}`;

    return {
      subject: `${config.site.name} - 密码重置`,
      html: `
        <!DOCTYPE html>
        <html>
        <head>
          <meta charset="utf-8">
          <title>密码重置</title>
          <style>
            body { font-family: Arial, sans-serif; line-height: 1.6; color: #333; }
            .container { max-width: 600px; margin: 0 auto; padding: 20px; }
            .header { background: #dc3545; color: white; padding: 20px; text-align: center; }
            .content { padding: 20px; background: #f9f9f9; }
            .footer { padding: 20px; text-align: center; color: #666; }
            .btn { display: inline-block; padding: 10px 20px; background: #dc3545; color: white; text-decoration: none; border-radius: 5px; }
            .warning { background: #fff3cd; border: 1px solid #ffeaa7; padding: 10px; border-radius: 5px; margin: 10px 0; }
          </style>
        </head>
        <body>
          <div class="container">
            <div class="header">
              <h1>密码重置请求</h1>
            </div>
            <div class="content">
              <h2>亲爱的 ${name}，</h2>
              <p>我们收到了您的密码重置请求。如果这不是您本人的操作，请忽略此邮件。</p>
              <div class="warning">
                <strong>安全提醒：</strong>此链接将在1小时后失效，请尽快完成密码重置。
              </div>
              <p style="text-align: center;">
                <a href="${resetUrl}" class="btn">重置密码</a>
              </p>
              <p>如果按钮无法点击，请复制以下链接到浏览器地址栏：</p>
              <p style="word-break: break-all; background: #f8f9fa; padding: 10px; border-radius: 5px;">
                ${resetUrl}
              </p>
            </div>
            <div class="footer">
              <p>此邮件由系统自动发送，请勿回复。</p>
              <p>${config.site.name} | ${config.site.url}</p>
            </div>
          </div>
        </body>
        </html>
      `,
      text: `
        密码重置请求

        亲爱的 ${name}，

        我们收到了您的密码重置请求。如果这不是您本人的操作，请忽略此邮件。

        安全提醒：此链接将在1小时后失效，请尽快完成密码重置。

        重置密码链接：${resetUrl}

        此邮件由系统自动发送，请勿回复。
        ${config.site.name} | ${config.site.url}
      `,
    };
  }

  /**
   * 获取联系咨询回复邮件模板
   */
  private getContactReplyTemplate(
    name: string,
    originalMessage: string,
    reply: string
  ): EmailTemplate {
    return {
      subject: `${config.site.name} - 咨询回复`,
      html: `
        <!DOCTYPE html>
        <html>
        <head>
          <meta charset="utf-8">
          <title>咨询回复</title>
          <style>
            body { font-family: Arial, sans-serif; line-height: 1.6; color: #333; }
            .container { max-width: 600px; margin: 0 auto; padding: 20px; }
            .header { background: #28a745; color: white; padding: 20px; text-align: center; }
            .content { padding: 20px; background: #f9f9f9; }
            .footer { padding: 20px; text-align: center; color: #666; }
            .message-box { background: white; padding: 15px; border-left: 4px solid #007bff; margin: 10px 0; }
            .reply-box { background: #e8f5e8; padding: 15px; border-left: 4px solid #28a745; margin: 10px 0; }
          </style>
        </head>
        <body>
          <div class="container">
            <div class="header">
              <h1>咨询回复</h1>
            </div>
            <div class="content">
              <h2>亲爱的 ${name}，</h2>
              <p>感谢您的咨询，我们已经为您准备了详细的回复。</p>

              <h3>您的咨询内容：</h3>
              <div class="message-box">
                ${originalMessage}
              </div>

              <h3>我们的回复：</h3>
              <div class="reply-box">
                ${reply}
              </div>

              <p>如果您还有其他问题，请随时联系我们。</p>
            </div>
            <div class="footer">
              <p>此邮件由系统自动发送，请勿回复。</p>
              <p>${config.site.name} | ${config.site.url}</p>
            </div>
          </div>
        </body>
        </html>
      `,
      text: `
        咨询回复

        亲爱的 ${name}，

        感谢您的咨询，我们已经为您准备了详细的回复。

        您的咨询内容：
        ${originalMessage}

        我们的回复：
        ${reply}

        如果您还有其他问题，请随时联系我们。

        此邮件由系统自动发送，请勿回复。
        ${config.site.name} | ${config.site.url}
      `,
    };
  }

  /**
   * 获取订单确认邮件模板
   */
  private getOrderConfirmationTemplate(name: string, orderDetails: any): EmailTemplate {
    return {
      subject: `${config.site.name} - 订单确认`,
      html: `
        <!DOCTYPE html>
        <html>
        <head>
          <meta charset="utf-8">
          <title>订单确认</title>
          <style>
            body { font-family: Arial, sans-serif; line-height: 1.6; color: #333; }
            .container { max-width: 600px; margin: 0 auto; padding: 20px; }
            .header { background: #007bff; color: white; padding: 20px; text-align: center; }
            .content { padding: 20px; background: #f9f9f9; }
            .footer { padding: 20px; text-align: center; color: #666; }
            .order-info { background: white; padding: 15px; border-radius: 5px; margin: 10px 0; }
          </style>
        </head>
        <body>
          <div class="container">
            <div class="header">
              <h1>订单确认</h1>
            </div>
            <div class="content">
              <h2>亲爱的 ${name}，</h2>
              <p>感谢您的订购！我们已经收到您的订单，正在为您安排发货。</p>

              <div class="order-info">
                <h3>订单信息：</h3>
                <p><strong>订单号：</strong>${orderDetails.orderNumber}</p>
                <p><strong>订单金额：</strong>¥${orderDetails.totalAmount}</p>
                <p><strong>下单时间：</strong>${orderDetails.orderDate}</p>
              </div>

              <p>我们会在1-2个工作日内为您发货，请保持手机畅通。</p>
            </div>
            <div class="footer">
              <p>此邮件由系统自动发送，请勿回复。</p>
              <p>${config.site.name} | ${config.site.url}</p>
            </div>
          </div>
        </body>
        </html>
      `,
      text: `
        订单确认

        亲爱的 ${name}，

        感谢您的订购！我们已经收到您的订单，正在为您安排发货。

        订单信息：
        订单号：${orderDetails.orderNumber}
        订单金额：¥${orderDetails.totalAmount}
        下单时间：${orderDetails.orderDate}

        我们会在1-2个工作日内为您发货，请保持手机畅通。

        此邮件由系统自动发送，请勿回复。
        ${config.site.name} | ${config.site.url}
      `,
    };
  }

  /**
   * 获取联系确认邮件模板
   */
  private getContactConfirmationTemplate(name: string, subject: string): EmailTemplate {
    return {
      subject: `${config.site.name} - 咨询确认`,
      html: `
        <!DOCTYPE html>
        <html>
        <head>
          <meta charset="utf-8">
          <title>咨询确认</title>
          <style>
            body { font-family: Arial, sans-serif; line-height: 1.6; color: #333; }
            .container { max-width: 600px; margin: 0 auto; padding: 20px; }
            .header { background: #007bff; color: white; padding: 20px; text-align: center; }
            .content { padding: 20px; background: #f9f9f9; }
            .footer { padding: 20px; text-align: center; color: #666; }
          </style>
        </head>
        <body>
          <div class="container">
            <div class="header">
              <h1>咨询确认</h1>
            </div>
            <div class="content">
              <h2>亲爱的 ${name}，</h2>
              <p>感谢您的咨询！我们已经收到您关于"${subject}"的咨询。</p>
              <p>我们的客服团队会在24小时内回复您，请保持邮箱畅通。</p>
              <p>如有紧急问题，请直接拨打我们的客服热线：400-123-4567</p>
            </div>
            <div class="footer">
              <p>此邮件由系统自动发送，请勿回复。</p>
              <p>${config.site.name} | ${config.site.url}</p>
            </div>
          </div>
        </body>
        </html>
      `,
      text: `
        咨询确认

        亲爱的 ${name}，

        感谢您的咨询！我们已经收到您关于"${subject}"的咨询。

        我们的客服团队会在24小时内回复您，请保持邮箱畅通。

        如有紧急问题，请直接拨打我们的客服热线：400-123-4567

        此邮件由系统自动发送，请勿回复。
        ${config.site.name} | ${config.site.url}
      `,
    };
  }

  /**
   * 获取新咨询通知邮件模板
   */
  private getNewInquiryNotificationTemplate(inquiry: any): EmailTemplate {
    return {
      subject: `${config.site.name} - 新的客户咨询`,
      html: `
        <!DOCTYPE html>
        <html>
        <head>
          <meta charset="utf-8">
          <title>新的客户咨询</title>
          <style>
            body { font-family: Arial, sans-serif; line-height: 1.6; color: #333; }
            .container { max-width: 600px; margin: 0 auto; padding: 20px; }
            .header { background: #28a745; color: white; padding: 20px; text-align: center; }
            .content { padding: 20px; background: #f9f9f9; }
            .info-box { background: white; padding: 15px; border-radius: 5px; margin: 10px 0; }
            .footer { padding: 20px; text-align: center; color: #666; }
          </style>
        </head>
        <body>
          <div class="container">
            <div class="header">
              <h1>新的客户咨询</h1>
            </div>
            <div class="content">
              <p>收到一条新的客户咨询，请及时处理：</p>

              <div class="info-box">
                <h3>客户信息：</h3>
                <p><strong>姓名：</strong>${inquiry.name}</p>
                <p><strong>邮箱：</strong>${inquiry.email}</p>
                ${inquiry.phone ? `<p><strong>电话：</strong>${inquiry.phone}</p>` : ''}
                ${inquiry.company ? `<p><strong>公司：</strong>${inquiry.company}</p>` : ''}
              </div>

              <div class="info-box">
                <h3>咨询内容：</h3>
                <p><strong>主题：</strong>${inquiry.subject}</p>
                <p><strong>内容：</strong></p>
                <p>${inquiry.message}</p>
              </div>

              <p>请登录管理后台查看详情并及时回复客户。</p>
            </div>
            <div class="footer">
              <p>此邮件由系统自动发送，请勿回复。</p>
              <p>${config.site.name} | ${config.site.url}</p>
            </div>
          </div>
        </body>
        </html>
      `,
      text: `
        新的客户咨询

        收到一条新的客户咨询，请及时处理：

        客户信息：
        姓名：${inquiry.name}
        邮箱：${inquiry.email}
        ${inquiry.phone ? `电话：${inquiry.phone}` : ''}
        ${inquiry.company ? `公司：${inquiry.company}` : ''}

        咨询内容：
        主题：${inquiry.subject}
        内容：${inquiry.message}

        请登录管理后台查看详情并及时回复客户。

        此邮件由系统自动发送，请勿回复。
        ${config.site.name} | ${config.site.url}
      `,
    };
  }

  /**
   * 验证邮件配置
   */
  async verifyConnection(): Promise<boolean> {
    try {
      await this.transporter.verify();
      logger.info('邮件服务连接验证成功');
      return true;
    } catch (error) {
      logger.error('邮件服务连接验证失败', { error });
      return false;
    }
  }
}
