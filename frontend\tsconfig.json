{"extends": "./.nuxt/tsconfig.json", "compilerOptions": {"strict": true, "noImplicitAny": true, "noImplicitReturns": true, "noImplicitThis": true, "noUnusedLocals": true, "noUnusedParameters": true, "exactOptionalPropertyTypes": true, "noImplicitOverride": true, "noPropertyAccessFromIndexSignature": true, "noUncheckedIndexedAccess": true, "allowUnusedLabels": false, "allowUnreachableCode": false, "baseUrl": ".", "paths": {"~/*": ["./*"], "@/*": ["./*"], "~~/*": ["./*"], "@@/*": ["./*"], "@/shared/*": ["../shared/*"]}}, "include": ["**/*", "../shared/**/*"], "exclude": ["node_modules", ".nuxt", ".output", "dist"]}