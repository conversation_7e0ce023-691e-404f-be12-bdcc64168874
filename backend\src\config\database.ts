/**
 * 数据库连接管理
 */

import { PrismaClient } from '@prisma/client';
import { config } from './index';
import { logger } from '../utils/logger';

/**
 * Prisma客户端配置
 */
const prismaConfig = {
  datasources: {
    db: {
      url: config.database.url,
    },
  },
  log: config.env === 'development' 
    ? ['query', 'info', 'warn', 'error'] as const
    : ['error'] as const,
  errorFormat: 'pretty' as const,
};

/**
 * 创建Prisma客户端实例
 */
export const prisma = new PrismaClient(prismaConfig);

/**
 * 数据库连接管理器
 */
export class DatabaseManager {
  private static instance: DatabaseManager;
  private prisma: PrismaClient;
  private isConnected: boolean = false;

  private constructor() {
    this.prisma = prisma;
  }

  /**
   * 获取单例实例
   */
  public static getInstance(): DatabaseManager {
    if (!DatabaseManager.instance) {
      DatabaseManager.instance = new DatabaseManager();
    }
    return DatabaseManager.instance;
  }

  /**
   * 连接数据库
   */
  public async connect(): Promise<void> {
    try {
      await this.prisma.$connect();
      this.isConnected = true;
      logger.info('✓ 数据库连接成功');
      
      // 测试连接
      await this.prisma.$queryRaw`SELECT 1`;
      logger.info('✓ 数据库连接测试通过');
    } catch (error) {
      logger.error('✗ 数据库连接失败:', error);
      throw error;
    }
  }

  /**
   * 断开数据库连接
   */
  public async disconnect(): Promise<void> {
    try {
      await this.prisma.$disconnect();
      this.isConnected = false;
      logger.info('✓ 数据库连接已断开');
    } catch (error) {
      logger.error('✗ 数据库断开连接失败:', error);
      throw error;
    }
  }

  /**
   * 检查数据库连接状态
   */
  public async checkConnection(): Promise<boolean> {
    try {
      await this.prisma.$queryRaw`SELECT 1`;
      return true;
    } catch {
      return false;
    }
  }

  /**
   * 获取Prisma客户端
   */
  public getPrisma(): PrismaClient {
    return this.prisma;
  }

  /**
   * 获取连接状态
   */
  public isConnectedToDatabase(): boolean {
    return this.isConnected;
  }

  /**
   * 健康检查
   */
  public async healthCheck(): Promise<{
    status: 'healthy' | 'unhealthy';
    details: {
      connected: boolean;
      responseTime: number;
      error?: string;
    };
  }> {
    const startTime = Date.now();
    
    try {
      const isConnected = await this.checkConnection();
      const responseTime = Date.now() - startTime;
      
      return {
        status: isConnected ? 'healthy' : 'unhealthy',
        details: {
          connected: isConnected,
          responseTime,
        },
      };
    } catch (error) {
      const responseTime = Date.now() - startTime;
      return {
        status: 'unhealthy',
        details: {
          connected: false,
          responseTime,
          error: error instanceof Error ? error.message : '未知错误',
        },
      };
    }
  }

  /**
   * 执行数据库迁移
   */
  public async migrate(): Promise<void> {
    try {
      logger.info('开始执行数据库迁移...');
      // 注意：在生产环境中，应该使用 prisma migrate deploy
      // 这里只是示例，实际迁移应该通过CLI执行
      logger.info('✓ 数据库迁移完成');
    } catch (error) {
      logger.error('✗ 数据库迁移失败:', error);
      throw error;
    }
  }

  /**
   * 执行数据库种子
   */
  public async seed(): Promise<void> {
    try {
      logger.info('开始执行数据库种子...');
      // 这里可以添加种子数据逻辑
      logger.info('✓ 数据库种子完成');
    } catch (error) {
      logger.error('✗ 数据库种子失败:', error);
      throw error;
    }
  }

  /**
   * 清理数据库连接
   */
  public async cleanup(): Promise<void> {
    try {
      // 清理长时间运行的查询
      await this.prisma.$queryRaw`KILL QUERY CONNECTION_ID()`;
      logger.info('✓ 数据库连接清理完成');
    } catch (error) {
      logger.warn('数据库连接清理警告:', error);
    }
  }

  /**
   * 获取数据库统计信息
   */
  public async getStats(): Promise<{
    tables: number;
    totalRecords: number;
    databaseSize: string;
  }> {
    try {
      // 获取表数量
      const tables = await this.prisma.$queryRaw<Array<{ count: number }>>`
        SELECT COUNT(*) as count 
        FROM information_schema.tables 
        WHERE table_schema = DATABASE()
      `;

      // 获取数据库大小
      const size = await this.prisma.$queryRaw<Array<{ size: string }>>`
        SELECT ROUND(SUM(data_length + index_length) / 1024 / 1024, 2) as size
        FROM information_schema.tables 
        WHERE table_schema = DATABASE()
      `;

      // 获取总记录数（这里只是示例，实际可能需要更复杂的查询）
      const userCount = await this.prisma.user.count();
      const productCount = await this.prisma.product.count();
      const articleCount = await this.prisma.article.count();
      
      return {
        tables: tables[0]?.count || 0,
        totalRecords: userCount + productCount + articleCount,
        databaseSize: `${size[0]?.size || 0} MB`,
      };
    } catch (error) {
      logger.error('获取数据库统计信息失败:', error);
      return {
        tables: 0,
        totalRecords: 0,
        databaseSize: '0 MB',
      };
    }
  }
}

// 优雅关闭处理
process.on('beforeExit', async () => {
  const dbManager = DatabaseManager.getInstance();
  if (dbManager.isConnectedToDatabase()) {
    await dbManager.disconnect();
  }
});

process.on('SIGINT', async () => {
  const dbManager = DatabaseManager.getInstance();
  if (dbManager.isConnectedToDatabase()) {
    await dbManager.disconnect();
  }
  process.exit(0);
});

process.on('SIGTERM', async () => {
  const dbManager = DatabaseManager.getInstance();
  if (dbManager.isConnectedToDatabase()) {
    await dbManager.disconnect();
  }
  process.exit(0);
});

// 导出默认实例
export default DatabaseManager.getInstance();
