/**
 * 应用配置
 */

import dotenv from 'dotenv';
import path from 'path';

// 加载环境变量
dotenv.config();

/**
 * 配置接口
 */
interface Config {
  env: string;
  port: number;
  host: string;
  database: {
    url: string;
  };
  jwt: {
    secret: string;
    expiresIn: string;
    refreshExpiresIn: string;
  };
  upload: {
    path: string;
    maxFileSize: number;
    maxImageSize: number;
    allowedImageTypes: string[];
    allowedFileTypes: string[];
  };
  email: {
    smtp: {
      host: string;
      port: number;
      secure: boolean;
      auth: {
        user: string;
        pass: string;
      };
    };
    from: {
      email: string;
      name: string;
    };
  };
  redis: {
    url: string;
    password?: string;
  };
  logging: {
    level: string;
    path: string;
  };
  security: {
    bcryptRounds: number;
  };
  rateLimit: {
    windowMs: number;
    maxRequests: number;
  };
  cors: {
    origin: string[];
  };
  site: {
    url: string;
    name: string;
    adminUrl: string;
  };
  thirdParty: {
    googleAnalytics?: string;
    baiduAnalytics?: string;
    cos?: {
      secretId: string;
      secretKey: string;
      bucket: string;
      region: string;
    };
    oss?: {
      accessKeyId: string;
      accessKeySecret: string;
      bucket: string;
      region: string;
    };
    sms?: {
      accessKeyId: string;
      accessKeySecret: string;
      signName: string;
      templateCode: string;
    };
  };
}

/**
 * 获取环境变量值
 */
function getEnvVar(key: string, defaultValue?: string): string {
  const value = process.env[key];
  if (!value && !defaultValue) {
    throw new Error(`环境变量 ${key} 未设置`);
  }
  return value || defaultValue!;
}

/**
 * 获取数字类型环境变量
 */
function getEnvNumber(key: string, defaultValue?: number): number {
  const value = process.env[key];
  if (!value && defaultValue === undefined) {
    throw new Error(`环境变量 ${key} 未设置`);
  }
  return value ? parseInt(value, 10) : defaultValue!;
}

/**
 * 获取布尔类型环境变量
 */
function getEnvBoolean(key: string, defaultValue?: boolean): boolean {
  const value = process.env[key];
  if (!value && defaultValue === undefined) {
    throw new Error(`环境变量 ${key} 未设置`);
  }
  return value ? value.toLowerCase() === 'true' : defaultValue!;
}

/**
 * 应用配置
 */
export const config: Config = {
  env: getEnvVar('NODE_ENV', 'development'),
  port: getEnvNumber('PORT', 3001),
  host: getEnvVar('HOST', 'localhost'),

  database: {
    url: getEnvVar('DATABASE_URL'),
  },

  jwt: {
    secret: getEnvVar('JWT_SECRET'),
    expiresIn: getEnvVar('JWT_EXPIRES_IN', '7d'),
    refreshExpiresIn: getEnvVar('JWT_REFRESH_EXPIRES_IN', '30d'),
  },

  upload: {
    path: path.resolve(getEnvVar('UPLOAD_PATH', './uploads')),
    maxFileSize: getEnvNumber('MAX_FILE_SIZE', 10 * 1024 * 1024), // 10MB
    maxImageSize: getEnvNumber('MAX_IMAGE_SIZE', 5 * 1024 * 1024), // 5MB
    allowedImageTypes: ['image/jpeg', 'image/png', 'image/webp', 'image/gif'],
    allowedFileTypes: [
      'application/pdf',
      'application/msword',
      'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
      'application/vnd.ms-excel',
      'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
    ],
  },

  email: {
    smtp: {
      host: getEnvVar('SMTP_HOST', 'smtp.gmail.com'),
      port: getEnvNumber('SMTP_PORT', 587),
      secure: getEnvBoolean('SMTP_SECURE', false),
      auth: {
        user: getEnvVar('SMTP_USER'),
        pass: getEnvVar('SMTP_PASS'),
      },
    },
    from: {
      email: getEnvVar('FROM_EMAIL'),
      name: getEnvVar('FROM_NAME', 'POS网站'),
    },
  },

  redis: {
    url: getEnvVar('REDIS_URL', 'redis://localhost:6379'),
    password: process.env.REDIS_PASSWORD,
  },

  logging: {
    level: getEnvVar('LOG_LEVEL', 'info'),
    path: path.resolve(getEnvVar('LOG_PATH', './logs')),
  },

  security: {
    bcryptRounds: getEnvNumber('BCRYPT_ROUNDS', 12),
  },

  rateLimit: {
    windowMs: getEnvNumber('RATE_LIMIT_WINDOW_MS', 15 * 60 * 1000), // 15分钟
    maxRequests: getEnvNumber('RATE_LIMIT_MAX_REQUESTS', 100),
  },

  cors: {
    origin: getEnvVar('CORS_ORIGIN', 'http://localhost:3000,http://localhost:3002').split(','),
  },

  site: {
    url: getEnvVar('SITE_URL', 'http://localhost:3000'),
    name: getEnvVar('SITE_NAME', 'POS机产品展示网站'),
    adminUrl: getEnvVar('ADMIN_URL', 'http://localhost:3002'),
  },

  thirdParty: {
    googleAnalytics: process.env.GA_TRACKING_ID,
    baiduAnalytics: process.env.BAIDU_ANALYTICS_ID,
    
    cos: process.env.COS_SECRET_ID ? {
      secretId: getEnvVar('COS_SECRET_ID'),
      secretKey: getEnvVar('COS_SECRET_KEY'),
      bucket: getEnvVar('COS_BUCKET'),
      region: getEnvVar('COS_REGION'),
    } : undefined,

    oss: process.env.OSS_ACCESS_KEY_ID ? {
      accessKeyId: getEnvVar('OSS_ACCESS_KEY_ID'),
      accessKeySecret: getEnvVar('OSS_ACCESS_KEY_SECRET'),
      bucket: getEnvVar('OSS_BUCKET'),
      region: getEnvVar('OSS_REGION'),
    } : undefined,

    sms: process.env.SMS_ACCESS_KEY_ID ? {
      accessKeyId: getEnvVar('SMS_ACCESS_KEY_ID'),
      accessKeySecret: getEnvVar('SMS_ACCESS_KEY_SECRET'),
      signName: getEnvVar('SMS_SIGN_NAME'),
      templateCode: getEnvVar('SMS_TEMPLATE_CODE'),
    } : undefined,
  },
};

/**
 * 验证配置
 */
export function validateConfig(): void {
  const requiredEnvVars = [
    'DATABASE_URL',
    'JWT_SECRET',
    'SMTP_USER',
    'SMTP_PASS',
    'FROM_EMAIL',
  ];

  const missingVars = requiredEnvVars.filter(varName => !process.env[varName]);

  if (missingVars.length > 0) {
    throw new Error(`缺少必需的环境变量: ${missingVars.join(', ')}`);
  }

  // 验证JWT密钥长度
  if (config.jwt.secret.length < 32) {
    throw new Error('JWT_SECRET 长度至少需要32个字符');
  }

  // 验证端口范围
  if (config.port < 1 || config.port > 65535) {
    throw new Error('PORT 必须在1-65535范围内');
  }

  // 验证bcrypt轮数
  if (config.security.bcryptRounds < 10 || config.security.bcryptRounds > 15) {
    throw new Error('BCRYPT_ROUNDS 建议在10-15范围内');
  }
}

// 在生产环境中验证配置
if (config.env === 'production') {
  validateConfig();
}
