---
description: 
globs: 
alwaysApply: true
---
# 开发规范与代码标准

## TypeScript 开发规范

### 类型定义规范
```typescript
// 接口定义 - 使用 I 前缀
interface IUser {
  id: number;
  name: string;
  email: string;
  createdAt: Date;
}

// 类型别名 - 使用 T 前缀
type TUserRole = 'admin' | 'user' | 'guest';

// 枚举定义 - 使用 PascalCase
enum UserStatus {
  ACTIVE = 'active',
  INACTIVE = 'inactive',
  PENDING = 'pending'
}

// 泛型约束
interface IApiResponse<T> {
  success: boolean;
  data: T;
  message?: string;
}
```

### 函数定义规范
```typescript
/**
 * 获取用户信息
 * @param userId - 用户ID
 * @param includeProfile - 是否包含用户资料
 * @returns Promise<IUser | null>
 */
async function getUserById(
  userId: number,
  includeProfile: boolean = false
): Promise<IUser | null> {
  // 实现逻辑
}

// 箭头函数
const formatDate = (date: Date, format: string = 'YYYY-MM-DD'): string => {
  // 实现逻辑
};
```

## Vue 3 组件开发规范

### 组件结构标准
```vue
<template>
  <!-- 模板内容 -->
  <div class="component-wrapper">
    <h1>{{ title }}</h1>
    <slot />
  </div>
</template>

<script setup lang="ts">
/**
 * 组件说明
 * @description 这是一个示例组件
 */

// 导入依赖
import { ref, computed, onMounted } from 'vue';
import type { IComponentProps } from '@/types/components';

// 定义 Props
interface Props {
  title: string;
  showHeader?: boolean;
}

const props = withDefaults(defineProps<Props>(), {
  showHeader: true
});

// 定义 Emits
interface Emits {
  (e: 'update', value: string): void;
  (e: 'close'): void;
}

const emit = defineEmits<Emits>();

// 响应式数据
const isLoading = ref(false);
const data = ref<IComponentProps[]>([]);

// 计算属性
const displayTitle = computed(() => {
  return props.showHeader ? props.title : '';
});

// 方法定义
const handleUpdate = (value: string): void => {
  emit('update', value);
};

// 生命周期
onMounted(() => {
  // 初始化逻辑
});
</script>

<style scoped>
.component-wrapper {
  @apply p-4 bg-white rounded-lg shadow-md;
}
</style>
```

### Composables 开发规范
```typescript
// composables/useApi.ts
import { ref, type Ref } from 'vue';
import type { IApiResponse } from '@/types/api';

/**
 * API 请求组合式函数
 * @param url - 请求地址
 * @returns 响应式数据和方法
 */
export function useApi<T>(url: string) {
  const data: Ref<T | null> = ref(null);
  const loading = ref(false);
  const error = ref<string | null>(null);

  const execute = async (): Promise<void> => {
    try {
      loading.value = true;
      error.value = null;
      
      const response = await $fetch<IApiResponse<T>>(url);
      data.value = response.data;
    } catch (err) {
      error.value = err instanceof Error ? err.message : '请求失败';
    } finally {
      loading.value = false;
    }
  };

  return {
    data: readonly(data),
    loading: readonly(loading),
    error: readonly(error),
    execute
  };
}
```

## Express 后端开发规范

### 控制器规范
```typescript
// controllers/product.controller.ts
import { Request, Response, NextFunction } from 'express';
import { ProductService } from '@/services/product.service';
import { ApiResponse } from '@/utils/response';
import { logger } from '@/utils/logger';
import type { IProduct, ICreateProductDto } from '@/types/product';

/**
 * 产品控制器
 */
export class ProductController {
  private productService: ProductService;

  constructor() {
    this.productService = new ProductService();
  }

  /**
   * 获取产品列表
   */
  public getProducts = async (
    req: Request,
    res: Response,
    next: NextFunction
  ): Promise<void> => {
    try {
      const { page = 1, limit = 10, category } = req.query;
      
      const products = await this.productService.getProducts({
        page: Number(page),
        limit: Number(limit),
        category: category as string
      });

      res.json(ApiResponse.success(products, '获取产品列表成功'));
    } catch (error) {
      logger.error('获取产品列表失败:', error);
      next(error);
    }
  };

  /**
   * 创建产品
   */
  public createProduct = async (
    req: Request,
    res: Response,
    next: NextFunction
  ): Promise<void> => {
    try {
      const productData: ICreateProductDto = req.body;
      
      const product = await this.productService.createProduct(productData);
      
      res.status(201).json(ApiResponse.success(product, '创建产品成功'));
    } catch (error) {
      logger.error('创建产品失败:', error);
      next(error);
    }
  };
}
```

### 服务层规范
```typescript
// services/product.service.ts
import { PrismaClient } from '@prisma/client';
import type { IProduct, ICreateProductDto, IProductQuery } from '@/types/product';

/**
 * 产品服务类
 */
export class ProductService {
  private prisma: PrismaClient;

  constructor() {
    this.prisma = new PrismaClient();
  }

  /**
   * 获取产品列表
   * @param query - 查询参数
   * @returns Promise<IProduct[]>
   */
  public async getProducts(query: IProductQuery): Promise<{
    products: IProduct[];
    total: number;
    page: number;
    limit: number;
  }> {
    const { page, limit, category } = query;
    const skip = (page - 1) * limit;

    const where = category ? { categoryId: category } : {};

    const [products, total] = await Promise.all([
      this.prisma.product.findMany({
        where,
        skip,
        take: limit,
        include: {
          category: true,
          images: true
        },
        orderBy: {
          createdAt: 'desc'
        }
      }),
      this.prisma.product.count({ where })
    ]);

    return {
      products,
      total,
      page,
      limit
    };
  }

  /**
   * 创建产品
   * @param data - 产品数据
   * @returns Promise<IProduct>
   */
  public async createProduct(data: ICreateProductDto): Promise<IProduct> {
    return await this.prisma.product.create({
      data,
      include: {
        category: true,
        images: true
      }
    });
  }
}
```

## 数据库设计规范

### Prisma Schema 规范
```prisma
// prisma/schema.prisma
generator client {
  provider = "prisma-client-js"
}

datasource db {
  provider = "mysql"
  url      = env("DATABASE_URL")
}

// 用户表
model User {
  id        Int      @id @default(autoincrement())
  email     String   @unique @db.VarChar(255)
  password  String   @db.VarChar(255)
  name      String   @db.VarChar(100)
  role      UserRole @default(USER)
  isActive  Boolean  @default(true) @map("is_active")
  createdAt DateTime @default(now()) @map("created_at")
  updatedAt DateTime @updatedAt @map("updated_at")

  @@map("users")
}

// 产品表
model Product {
  id          Int      @id @default(autoincrement())
  name        String   @db.VarChar(255)
  slug        String   @unique @db.VarChar(255)
  description String?  @db.Text
  price       Decimal  @db.Decimal(10, 2)
  categoryId  Int      @map("category_id")
  isActive    Boolean  @default(true) @map("is_active")
  createdAt   DateTime @default(now()) @map("created_at")
  updatedAt   DateTime @updatedAt @map("updated_at")

  // 关联关系
  category Category @relation(fields: [categoryId], references: [id])
  images   ProductImage[]

  @@map("products")
}

enum UserRole {
  ADMIN
  USER
}
```

## 错误处理规范

### 统一错误处理
```typescript
// utils/errors.ts
export class AppError extends Error {
  public statusCode: number;
  public isOperational: boolean;

  constructor(message: string, statusCode: number = 500) {
    super(message);
    this.statusCode = statusCode;
    this.isOperational = true;

    Error.captureStackTrace(this, this.constructor);
  }
}

// 业务错误类型
export class ValidationError extends AppError {
  constructor(message: string) {
    super(message, 400);
  }
}

export class NotFoundError extends AppError {
  constructor(resource: string) {
    super(`${resource} 未找到`, 404);
  }
}

export class UnauthorizedError extends AppError {
  constructor(message: string = '未授权访问') {
    super(message, 401);
  }
}
```

## 日志记录规范

### Winston 日志配置
```typescript
// utils/logger.ts
import winston from 'winston';
import path from 'path';

const logFormat = winston.format.combine(
  winston.format.timestamp({ format: 'YYYY-MM-DD HH:mm:ss' }),
  winston.format.errors({ stack: true }),
  winston.format.json()
);

export const logger = winston.createLogger({
  level: process.env.LOG_LEVEL || 'info',
  format: logFormat,
  transports: [
    // 控制台输出
    new winston.transports.Console({
      format: winston.format.combine(
        winston.format.colorize(),
        winston.format.simple()
      )
    }),
    // 错误日志文件
    new winston.transports.File({
      filename: path.join('logs', 'error.log'),
      level: 'error'
    }),
    // 所有日志文件
    new winston.transports.File({
      filename: path.join('logs', 'combined.log')
    })
  ]
});
```

## 测试规范

### 单元测试规范
```typescript
// tests/services/product.service.test.ts
import { describe, it, expect, beforeEach, afterEach } from 'vitest';
import { ProductService } from '@/services/product.service';
import type { ICreateProductDto } from '@/types/product';

describe('ProductService', () => {
  let productService: ProductService;

  beforeEach(() => {
    productService = new ProductService();
  });

  afterEach(() => {
    // 清理工作
  });

  describe('getProducts', () => {
    it('应该返回产品列表', async () => {
      // Arrange
      const query = { page: 1, limit: 10 };

      // Act
      const result = await productService.getProducts(query);

      // Assert
      expect(result).toHaveProperty('products');
      expect(result).toHaveProperty('total');
      expect(Array.isArray(result.products)).toBe(true);
    });
  });

  describe('createProduct', () => {
    it('应该成功创建产品', async () => {
      // Arrange
      const productData: ICreateProductDto = {
        name: '测试产品',
        slug: 'test-product',
        description: '测试描述',
        price: 99.99,
        categoryId: 1
      };

      // Act
      const result = await productService.createProduct(productData);

      // Assert
      expect(result).toHaveProperty('id');
      expect(result.name).toBe(productData.name);
    });
  });
});
```

## Git 提交规范

### 提交消息格式
```
<type>(<scope>): <subject>

<body>

<footer>
```

### 提交类型
- **feat**: 新功能
- **fix**: 修复bug
- **docs**: 文档更新
- **style**: 代码格式调整
- **refactor**: 代码重构
- **test**: 测试相关
- **chore**: 构建过程或辅助工具的变动

### 示例
```
feat(product): 添加产品搜索功能

- 实现产品名称搜索
- 添加分类筛选
- 优化搜索性能

Closes #123

