/**
 * 分类路由
 */

import { Router } from 'express';
import { body } from 'express-validator';
import { CategoryController } from '../controllers/category.controller';
import { authMiddleware, requireAdmin, requireEditor } from '../middleware/auth';
import { asyncHandler } from '../middleware/errorHandler';
import { validateRequest } from '../middleware/validation';

const router = Router();
const categoryController = new CategoryController();

/**
 * 分类创建验证规则
 */
const createCategoryValidation = [
  body('name')
    .trim()
    .isLength({ min: 2, max: 100 })
    .withMessage('分类名称长度必须在2-100个字符之间'),
  body('slug')
    .trim()
    .isLength({ min: 2, max: 100 })
    .matches(/^[a-z0-9]+(?:-[a-z0-9]+)*$/)
    .withMessage('分类标识符格式不正确'),
  body('description')
    .optional()
    .isLength({ max: 1000 })
    .withMessage('分类描述不能超过1000个字符'),
  body('parentId')
    .optional()
    .isInt({ min: 1 })
    .withMessage('父分类ID必须是正整数'),
  body('sortOrder')
    .optional()
    .isInt({ min: 0 })
    .withMessage('排序值必须是非负整数'),
];

/**
 * 分类更新验证规则
 */
const updateCategoryValidation = [
  body('name')
    .optional()
    .trim()
    .isLength({ min: 2, max: 100 })
    .withMessage('分类名称长度必须在2-100个字符之间'),
  body('slug')
    .optional()
    .trim()
    .isLength({ min: 2, max: 100 })
    .matches(/^[a-z0-9]+(?:-[a-z0-9]+)*$/)
    .withMessage('分类标识符格式不正确'),
  body('description')
    .optional()
    .isLength({ max: 1000 })
    .withMessage('分类描述不能超过1000个字符'),
  body('parentId')
    .optional()
    .isInt({ min: 1 })
    .withMessage('父分类ID必须是正整数'),
  body('sortOrder')
    .optional()
    .isInt({ min: 0 })
    .withMessage('排序值必须是非负整数'),
];

/**
 * @swagger
 * /api/categories:
 *   get:
 *     summary: 获取分类列表
 *     tags: [分类]
 *     parameters:
 *       - in: query
 *         name: includeProducts
 *         schema:
 *           type: boolean
 *         description: 是否包含产品信息
 *     responses:
 *       200:
 *         description: 获取成功
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                 data:
 *                   type: array
 *                   items:
 *                     $ref: '#/components/schemas/Category'
 */
router.get('/',
  asyncHandler(categoryController.getCategories.bind(categoryController))
);

/**
 * @swagger
 * /api/categories/tree:
 *   get:
 *     summary: 获取分类树结构
 *     tags: [分类]
 *     responses:
 *       200:
 *         description: 获取成功
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                 data:
 *                   type: array
 *                   items:
 *                     $ref: '#/components/schemas/CategoryTree'
 */
router.get('/tree',
  asyncHandler(categoryController.getCategoryTree.bind(categoryController))
);

/**
 * @swagger
 * /api/categories:
 *   post:
 *     summary: 创建分类
 *     tags: [分类]
 *     security:
 *       - bearerAuth: []
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             $ref: '#/components/schemas/CreateCategoryDto'
 *     responses:
 *       201:
 *         description: 创建成功
 *       400:
 *         description: 请求参数错误
 *       401:
 *         description: 未授权
 *       403:
 *         description: 权限不足
 */
router.post('/',
  authMiddleware,
  requireEditor,
  createCategoryValidation,
  validateRequest,
  asyncHandler(categoryController.createCategory.bind(categoryController))
);

/**
 * @swagger
 * /api/categories/{slug}:
 *   get:
 *     summary: 获取分类详情
 *     tags: [分类]
 *     parameters:
 *       - in: path
 *         name: slug
 *         required: true
 *         schema:
 *           type: string
 *         description: 分类标识符
 *     responses:
 *       200:
 *         description: 获取成功
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                 data:
 *                   $ref: '#/components/schemas/CategoryDetail'
 *       404:
 *         description: 分类不存在
 */
router.get('/:slug',
  asyncHandler(categoryController.getCategory.bind(categoryController))
);

/**
 * @swagger
 * /api/categories/{id}:
 *   put:
 *     summary: 更新分类
 *     tags: [分类]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: id
 *         required: true
 *         schema:
 *           type: integer
 *         description: 分类ID
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             $ref: '#/components/schemas/UpdateCategoryDto'
 *     responses:
 *       200:
 *         description: 更新成功
 *       400:
 *         description: 请求参数错误
 *       401:
 *         description: 未授权
 *       403:
 *         description: 权限不足
 *       404:
 *         description: 分类不存在
 */
router.put('/:id',
  authMiddleware,
  requireEditor,
  updateCategoryValidation,
  validateRequest,
  asyncHandler(categoryController.updateCategory.bind(categoryController))
);

/**
 * @swagger
 * /api/categories/{id}:
 *   delete:
 *     summary: 删除分类
 *     tags: [分类]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: id
 *         required: true
 *         schema:
 *           type: integer
 *         description: 分类ID
 *     responses:
 *       200:
 *         description: 删除成功
 *       400:
 *         description: 分类下有子分类或产品，无法删除
 *       401:
 *         description: 未授权
 *       403:
 *         description: 权限不足
 *       404:
 *         description: 分类不存在
 */
router.delete('/:id',
  authMiddleware,
  requireAdmin,
  asyncHandler(categoryController.deleteCategory.bind(categoryController))
);

export default router;
