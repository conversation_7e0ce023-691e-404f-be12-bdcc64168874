/**
 * 统计分析控制器
 */

import { Request, Response } from 'express';
import { DatabaseManager } from '../config/database';
import { logger } from '../utils/logger';
import { ApiResponse } from '../utils/response';
import UAParser from 'ua-parser-js';

/**
 * 统计分析控制器
 */
export class AnalyticsController {
  private prisma = DatabaseManager.getInstance().getPrisma();

  /**
   * 记录页面访问
   */
  public async trackPageView(req: Request, res: Response): Promise<void> {
    const { path, title, referrer, userAgent } = req.body;

    try {
      // 解析用户代理
      const parser = new UAParser(userAgent);
      const device = parser.getDevice();
      const browser = parser.getBrowser();
      const os = parser.getOS();

      // 获取IP地址
      const ipAddress = req.ip || req.connection.remoteAddress || '';

      // 记录页面访问
      await this.prisma.pageView.create({
        data: {
          path,
          title,
          referrer,
          ipAddress,
          userAgent,
          deviceType: device.type || 'desktop',
          deviceVendor: device.vendor || '',
          deviceModel: device.model || '',
          browserName: browser.name || '',
          browserVersion: browser.version || '',
          osName: os.name || '',
          osVersion: os.version || '',
          userId: req.user?.id,
        },
      });

      res.json(ApiResponse.success(null, '访问记录成功'));
    } catch (error) {
      logger.error('记录页面访问失败', { error, data: req.body });
      // 统计失败不应该影响用户体验，返回成功
      res.json(ApiResponse.success(null, '访问记录成功'));
    }
  }

  /**
   * 获取统计概览
   */
  public async getOverview(req: Request, res: Response): Promise<void> {
    const { period = 'week' } = req.query;

    try {
      const dateRange = this.getDateRange(period as string);

      // 获取页面访问量
      const pageViews = await this.prisma.pageView.count({
        where: {
          createdAt: {
            gte: dateRange.start,
            lte: dateRange.end,
          },
        },
      });

      // 获取独立访客数
      const uniqueVisitors = await this.prisma.pageView.groupBy({
        by: ['ipAddress'],
        where: {
          createdAt: {
            gte: dateRange.start,
            lte: dateRange.end,
          },
        },
        _count: {
          ipAddress: true,
        },
      });

      // 获取热门页面
      const topPages = await this.prisma.pageView.groupBy({
        by: ['path'],
        where: {
          createdAt: {
            gte: dateRange.start,
            lte: dateRange.end,
          },
        },
        _count: {
          path: true,
        },
        orderBy: {
          _count: {
            path: 'desc',
          },
        },
        take: 10,
      });

      // 获取设备类型分布
      const deviceTypes = await this.prisma.pageView.groupBy({
        by: ['deviceType'],
        where: {
          createdAt: {
            gte: dateRange.start,
            lte: dateRange.end,
          },
        },
        _count: {
          deviceType: true,
        },
      });

      // 获取浏览器分布
      const browsers = await this.prisma.pageView.groupBy({
        by: ['browserName'],
        where: {
          createdAt: {
            gte: dateRange.start,
            lte: dateRange.end,
          },
          browserName: {
            not: '',
          },
        },
        _count: {
          browserName: true,
        },
        orderBy: {
          _count: {
            browserName: 'desc',
          },
        },
        take: 5,
      });

      const overview = {
        pageViews,
        uniqueVisitors: uniqueVisitors.length,
        topPages: topPages.map(page => ({
          path: page.path,
          views: page._count.path,
        })),
        deviceTypes: deviceTypes.map(device => ({
          type: device.deviceType,
          count: device._count.deviceType,
        })),
        browsers: browsers.map(browser => ({
          name: browser.browserName,
          count: browser._count.browserName,
        })),
        period,
        dateRange,
      };

      res.json(ApiResponse.success(overview));
    } catch (error) {
      logger.error('获取统计概览失败', { error, period });
      throw error;
    }
  }

  /**
   * 获取页面访问统计
   */
  public async getPageStats(req: Request, res: Response): Promise<void> {
    const { period = 'week', limit = 20 } = req.query;

    try {
      const dateRange = this.getDateRange(period as string);

      const pageStats = await this.prisma.pageView.groupBy({
        by: ['path', 'title'],
        where: {
          createdAt: {
            gte: dateRange.start,
            lte: dateRange.end,
          },
        },
        _count: {
          path: true,
        },
        orderBy: {
          _count: {
            path: 'desc',
          },
        },
        take: Number(limit),
      });

      const result = pageStats.map(stat => ({
        path: stat.path,
        title: stat.title,
        views: stat._count.path,
      }));

      res.json(ApiResponse.success(result));
    } catch (error) {
      logger.error('获取页面访问统计失败', { error, period });
      throw error;
    }
  }

  /**
   * 获取访客统计
   */
  public async getVisitorStats(req: Request, res: Response): Promise<void> {
    const { period = 'week' } = req.query;

    try {
      const dateRange = this.getDateRange(period as string);

      // 按日期分组统计访客数
      const dailyStats = await this.prisma.$queryRaw<Array<{
        date: string;
        pageViews: number;
        uniqueVisitors: number;
      }>>`
        SELECT 
          DATE(createdAt) as date,
          COUNT(*) as pageViews,
          COUNT(DISTINCT ipAddress) as uniqueVisitors
        FROM PageView 
        WHERE createdAt >= ${dateRange.start} AND createdAt <= ${dateRange.end}
        GROUP BY DATE(createdAt)
        ORDER BY date ASC
      `;

      res.json(ApiResponse.success(dailyStats));
    } catch (error) {
      logger.error('获取访客统计失败', { error, period });
      throw error;
    }
  }

  /**
   * 获取设备统计
   */
  public async getDeviceStats(req: Request, res: Response): Promise<void> {
    const { period = 'week' } = req.query;

    try {
      const dateRange = this.getDateRange(period as string);

      // 设备类型统计
      const deviceTypes = await this.prisma.pageView.groupBy({
        by: ['deviceType'],
        where: {
          createdAt: {
            gte: dateRange.start,
            lte: dateRange.end,
          },
        },
        _count: {
          deviceType: true,
        },
      });

      // 操作系统统计
      const operatingSystems = await this.prisma.pageView.groupBy({
        by: ['osName'],
        where: {
          createdAt: {
            gte: dateRange.start,
            lte: dateRange.end,
          },
          osName: {
            not: '',
          },
        },
        _count: {
          osName: true,
        },
        orderBy: {
          _count: {
            osName: 'desc',
          },
        },
        take: 10,
      });

      // 浏览器统计
      const browsers = await this.prisma.pageView.groupBy({
        by: ['browserName'],
        where: {
          createdAt: {
            gte: dateRange.start,
            lte: dateRange.end,
          },
          browserName: {
            not: '',
          },
        },
        _count: {
          browserName: true,
        },
        orderBy: {
          _count: {
            browserName: 'desc',
          },
        },
        take: 10,
      });

      const stats = {
        deviceTypes: deviceTypes.map(device => ({
          type: device.deviceType,
          count: device._count.deviceType,
        })),
        operatingSystems: operatingSystems.map(os => ({
          name: os.osName,
          count: os._count.osName,
        })),
        browsers: browsers.map(browser => ({
          name: browser.browserName,
          count: browser._count.browserName,
        })),
      };

      res.json(ApiResponse.success(stats));
    } catch (error) {
      logger.error('获取设备统计失败', { error, period });
      throw error;
    }
  }

  /**
   * 获取来源统计
   */
  public async getReferrerStats(req: Request, res: Response): Promise<void> {
    const { period = 'week', limit = 20 } = req.query;

    try {
      const dateRange = this.getDateRange(period as string);

      const referrerStats = await this.prisma.pageView.groupBy({
        by: ['referrer'],
        where: {
          createdAt: {
            gte: dateRange.start,
            lte: dateRange.end,
          },
          referrer: {
            not: '',
          },
        },
        _count: {
          referrer: true,
        },
        orderBy: {
          _count: {
            referrer: 'desc',
          },
        },
        take: Number(limit),
      });

      const result = referrerStats.map(stat => ({
        referrer: stat.referrer,
        visits: stat._count.referrer,
      }));

      res.json(ApiResponse.success(result));
    } catch (error) {
      logger.error('获取来源统计失败', { error, period });
      throw error;
    }
  }

  /**
   * 获取日期范围
   */
  private getDateRange(period: string): { start: Date; end: Date } {
    const end = new Date();
    const start = new Date();

    switch (period) {
      case 'today':
        start.setHours(0, 0, 0, 0);
        break;
      case 'week':
        start.setDate(start.getDate() - 7);
        break;
      case 'month':
        start.setMonth(start.getMonth() - 1);
        break;
      case 'year':
        start.setFullYear(start.getFullYear() - 1);
        break;
      default:
        start.setDate(start.getDate() - 7);
    }

    return { start, end };
  }
}
