@import 'tailwindcss/base';
@import 'tailwindcss/components';
@import 'tailwindcss/utilities';

/* 自定义CSS变量 */
:root {
  --primary-color: #007bff;
  --primary-dark: #0056b3;
  --secondary-color: #6c757d;
  --success-color: #28a745;
  --danger-color: #dc3545;
  --warning-color: #ffc107;
  --info-color: #17a2b8;
  --light-color: #f8f9fa;
  --dark-color: #343a40;
  
  --font-family-sans: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
  --font-family-mono: 'JetBrains Mono', 'Fira Code', Consolas, monospace;
  
  --border-radius: 0.375rem;
  --border-radius-lg: 0.5rem;
  --border-radius-xl: 0.75rem;
  
  --shadow-sm: 0 1px 2px 0 rgb(0 0 0 / 0.05);
  --shadow: 0 1px 3px 0 rgb(0 0 0 / 0.1), 0 1px 2px -1px rgb(0 0 0 / 0.1);
  --shadow-md: 0 4px 6px -1px rgb(0 0 0 / 0.1), 0 2px 4px -2px rgb(0 0 0 / 0.1);
  --shadow-lg: 0 10px 15px -3px rgb(0 0 0 / 0.1), 0 4px 6px -4px rgb(0 0 0 / 0.1);
  --shadow-xl: 0 20px 25px -5px rgb(0 0 0 / 0.1), 0 8px 10px -6px rgb(0 0 0 / 0.1);
}

/* 基础样式重置 */
* {
  box-sizing: border-box;
}

html {
  scroll-behavior: smooth;
  -webkit-text-size-adjust: 100%;
}

body {
  font-family: var(--font-family-sans);
  line-height: 1.6;
  color: #333;
  background-color: #fff;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

/* 链接样式 */
a {
  color: var(--primary-color);
  text-decoration: none;
  transition: color 0.2s ease;
}

a:hover {
  color: var(--primary-dark);
}

/* 按钮基础样式 */
.btn {
  @apply inline-flex items-center justify-center px-4 py-2 text-sm font-medium rounded-md border border-transparent transition-colors duration-200 focus:outline-none focus:ring-2 focus:ring-offset-2 disabled:opacity-50 disabled:cursor-not-allowed;
}

.btn-primary {
  @apply bg-blue-600 text-white hover:bg-blue-700 focus:ring-blue-500;
}

.btn-secondary {
  @apply bg-gray-600 text-white hover:bg-gray-700 focus:ring-gray-500;
}

.btn-success {
  @apply bg-green-600 text-white hover:bg-green-700 focus:ring-green-500;
}

.btn-danger {
  @apply bg-red-600 text-white hover:bg-red-700 focus:ring-red-500;
}

.btn-warning {
  @apply bg-yellow-500 text-white hover:bg-yellow-600 focus:ring-yellow-400;
}

.btn-outline {
  @apply bg-transparent border-current hover:bg-current hover:text-white;
}

.btn-sm {
  @apply px-3 py-1.5 text-xs;
}

.btn-lg {
  @apply px-6 py-3 text-base;
}

/* 卡片样式 */
.card {
  @apply bg-white rounded-lg shadow-md border border-gray-200 overflow-hidden;
}

.card-header {
  @apply px-6 py-4 border-b border-gray-200 bg-gray-50;
}

.card-body {
  @apply px-6 py-4;
}

.card-footer {
  @apply px-6 py-4 border-t border-gray-200 bg-gray-50;
}

/* 表单样式 */
.form-group {
  @apply mb-4;
}

.form-label {
  @apply block text-sm font-medium text-gray-700 mb-2;
}

.form-input {
  @apply w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-colors duration-200;
}

.form-textarea {
  @apply form-input resize-vertical;
}

.form-select {
  @apply form-input pr-10 bg-white;
}

.form-error {
  @apply mt-1 text-sm text-red-600;
}

.form-help {
  @apply mt-1 text-sm text-gray-500;
}

/* 网格布局 */
.container {
  @apply max-w-7xl mx-auto px-4 sm:px-6 lg:px-8;
}

.container-sm {
  @apply max-w-3xl mx-auto px-4 sm:px-6 lg:px-8;
}

.container-xs {
  @apply max-w-xl mx-auto px-4 sm:px-6 lg:px-8;
}

/* 响应式网格 */
.grid-cols-auto {
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
}

.grid-cols-auto-sm {
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
}

.grid-cols-auto-lg {
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
}

/* 文本样式 */
.text-gradient {
  @apply bg-gradient-to-r from-blue-600 to-purple-600 bg-clip-text text-transparent;
}

.text-balance {
  text-wrap: balance;
}

/* 动画效果 */
.fade-in {
  animation: fadeIn 0.5s ease-in-out;
}

.slide-up {
  animation: slideUp 0.5s ease-out;
}

.scale-in {
  animation: scaleIn 0.3s ease-out;
}

@keyframes fadeIn {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

@keyframes slideUp {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes scaleIn {
  from {
    opacity: 0;
    transform: scale(0.9);
  }
  to {
    opacity: 1;
    transform: scale(1);
  }
}

/* 加载动画 */
.loading-spinner {
  @apply inline-block w-4 h-4 border-2 border-current border-t-transparent rounded-full animate-spin;
}

.loading-dots {
  @apply inline-flex space-x-1;
}

.loading-dots > div {
  @apply w-2 h-2 bg-current rounded-full animate-pulse;
}

.loading-dots > div:nth-child(2) {
  animation-delay: 0.1s;
}

.loading-dots > div:nth-child(3) {
  animation-delay: 0.2s;
}

/* 滚动条样式 */
.scrollbar-thin {
  scrollbar-width: thin;
  scrollbar-color: #cbd5e0 #f7fafc;
}

.scrollbar-thin::-webkit-scrollbar {
  width: 6px;
  height: 6px;
}

.scrollbar-thin::-webkit-scrollbar-track {
  background: #f7fafc;
}

.scrollbar-thin::-webkit-scrollbar-thumb {
  background: #cbd5e0;
  border-radius: 3px;
}

.scrollbar-thin::-webkit-scrollbar-thumb:hover {
  background: #a0aec0;
}

/* 图片样式 */
.img-responsive {
  @apply w-full h-auto;
}

.img-cover {
  @apply w-full h-full object-cover;
}

.img-contain {
  @apply w-full h-full object-contain;
}

/* 阴影效果 */
.shadow-soft {
  box-shadow: 0 2px 15px -3px rgba(0, 0, 0, 0.07), 0 10px 20px -2px rgba(0, 0, 0, 0.04);
}

.shadow-colored {
  box-shadow: 0 4px 14px 0 rgba(59, 130, 246, 0.15);
}

/* 渐变背景 */
.bg-gradient-primary {
  @apply bg-gradient-to-r from-blue-600 to-blue-700;
}

.bg-gradient-secondary {
  @apply bg-gradient-to-r from-gray-600 to-gray-700;
}

.bg-gradient-success {
  @apply bg-gradient-to-r from-green-600 to-green-700;
}

.bg-gradient-hero {
  @apply bg-gradient-to-br from-blue-600 via-purple-600 to-blue-800;
}

/* 响应式工具类 */
@media (max-width: 640px) {
  .container {
    @apply px-4;
  }
  
  .text-responsive {
    @apply text-sm;
  }
  
  .btn-responsive {
    @apply px-3 py-2 text-sm;
  }
}

/* 打印样式 */
@media print {
  .no-print {
    display: none !important;
  }
  
  .print-only {
    display: block !important;
  }
  
  body {
    color: black !important;
    background: white !important;
  }
  
  a {
    color: black !important;
    text-decoration: underline !important;
  }
}

/* 深色模式支持 */
@media (prefers-color-scheme: dark) {
  .dark-mode-auto {
    @apply bg-gray-900 text-white;
  }
  
  .dark-mode-auto .card {
    @apply bg-gray-800 border-gray-700;
  }
  
  .dark-mode-auto .form-input {
    @apply bg-gray-800 border-gray-600 text-white;
  }
}
