/**
 * 统计分析路由
 */

import { Router } from 'express';
import { body } from 'express-validator';
import { AnalyticsController } from '../controllers/analytics.controller';
import { requireAdmin } from '../middleware/auth';
import { asyncHandler } from '../middleware/errorHandler';
import { validateRequest } from '../middleware/validation';

const router = Router();
const analyticsController = new AnalyticsController();

/**
 * 页面访问统计验证规则
 */
const trackPageViewValidation = [
  body('path')
    .trim()
    .isLength({ min: 1, max: 500 })
    .withMessage('页面路径长度必须在1-500个字符之间'),
  body('title')
    .optional()
    .isLength({ max: 255 })
    .withMessage('页面标题不能超过255个字符'),
  body('referrer')
    .optional()
    .isLength({ max: 500 })
    .withMessage('来源页面不能超过500个字符'),
  body('userAgent')
    .optional()
    .isLength({ max: 1000 })
    .withMessage('用户代理不能超过1000个字符'),
];

/**
 * @swagger
 * /api/analytics/track:
 *   post:
 *     summary: 记录页面访问
 *     tags: [统计分析]
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required:
 *               - path
 *             properties:
 *               path:
 *                 type: string
 *                 description: 页面路径
 *               title:
 *                 type: string
 *                 description: 页面标题
 *               referrer:
 *                 type: string
 *                 description: 来源页面
 *               userAgent:
 *                 type: string
 *                 description: 用户代理
 *     responses:
 *       200:
 *         description: 记录成功
 */
router.post('/track',
  trackPageViewValidation,
  validateRequest,
  asyncHandler(analyticsController.trackPageView.bind(analyticsController))
);

/**
 * @swagger
 * /api/analytics/overview:
 *   get:
 *     summary: 获取统计概览
 *     tags: [统计分析]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: query
 *         name: period
 *         schema:
 *           type: string
 *           enum: [today, week, month, year]
 *         description: 统计周期
 *     responses:
 *       200:
 *         description: 获取成功
 *       401:
 *         description: 未授权
 *       403:
 *         description: 权限不足
 */
router.get('/overview',
  requireAdmin,
  asyncHandler(analyticsController.getOverview.bind(analyticsController))
);

/**
 * @swagger
 * /api/analytics/pages:
 *   get:
 *     summary: 获取页面访问统计
 *     tags: [统计分析]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: query
 *         name: period
 *         schema:
 *           type: string
 *           enum: [today, week, month, year]
 *         description: 统计周期
 *       - in: query
 *         name: limit
 *         schema:
 *           type: integer
 *           minimum: 1
 *           maximum: 100
 *         description: 返回数量限制
 *     responses:
 *       200:
 *         description: 获取成功
 *       401:
 *         description: 未授权
 *       403:
 *         description: 权限不足
 */
router.get('/pages',
  requireAdmin,
  asyncHandler(analyticsController.getPageStats.bind(analyticsController))
);

/**
 * @swagger
 * /api/analytics/visitors:
 *   get:
 *     summary: 获取访客统计
 *     tags: [统计分析]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: query
 *         name: period
 *         schema:
 *           type: string
 *           enum: [today, week, month, year]
 *         description: 统计周期
 *     responses:
 *       200:
 *         description: 获取成功
 *       401:
 *         description: 未授权
 *       403:
 *         description: 权限不足
 */
router.get('/visitors',
  requireAdmin,
  asyncHandler(analyticsController.getVisitorStats.bind(analyticsController))
);

/**
 * @swagger
 * /api/analytics/devices:
 *   get:
 *     summary: 获取设备统计
 *     tags: [统计分析]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: query
 *         name: period
 *         schema:
 *           type: string
 *           enum: [today, week, month, year]
 *         description: 统计周期
 *     responses:
 *       200:
 *         description: 获取成功
 *       401:
 *         description: 未授权
 *       403:
 *         description: 权限不足
 */
router.get('/devices',
  requireAdmin,
  asyncHandler(analyticsController.getDeviceStats.bind(analyticsController))
);

/**
 * @swagger
 * /api/analytics/referrers:
 *   get:
 *     summary: 获取来源统计
 *     tags: [统计分析]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: query
 *         name: period
 *         schema:
 *           type: string
 *           enum: [today, week, month, year]
 *         description: 统计周期
 *       - in: query
 *         name: limit
 *         schema:
 *           type: integer
 *           minimum: 1
 *           maximum: 100
 *         description: 返回数量限制
 *     responses:
 *       200:
 *         description: 获取成功
 *       401:
 *         description: 未授权
 *       403:
 *         description: 权限不足
 */
router.get('/referrers',
  requireAdmin,
  asyncHandler(analyticsController.getReferrerStats.bind(analyticsController))
);

export default router;
