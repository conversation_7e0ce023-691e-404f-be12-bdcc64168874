---
description: 
globs: 
alwaysApply: true
---
# 安全规范与最佳实践

## 前端安全

### XSS防护
```typescript
// utils/security.ts
export class SecurityUtils {
  /**
   * HTML转义防止XSS
   */
  static escapeHtml(text: string): string {
    const map: Record<string, string> = {
      '&': '&amp;',
      '<': '&lt;',
      '>': '&gt;',
      '"': '&quot;',
      "'": '&#039;'
    };
    
    return text.replace(/[&<>"']/g, (m) => map[m]);
  }

  /**
   * 清理用户输入
   */
  static sanitizeInput(input: string): string {
    // 移除潜在危险字符
    return input
      .replace(/<script\b[^<]*(?:(?!<\/script>)<[^<]*)*<\/script>/gi, '')
      .replace(/javascript:/gi, '')
      .replace(/on\w+\s*=/gi, '')
      .trim();
  }

  /**
   * 验证URL安全性
   */
  static isValidUrl(url: string): boolean {
    try {
      const urlObj = new URL(url);
      // 只允许http和https协议
      return ['http:', 'https:'].includes(urlObj.protocol);
    } catch {
      return false;
    }
  }

  /**
   * CSP内容安全策略
   */
  static getCSPHeader(): string {
    return [
      "default-src 'self'",
      "script-src 'self' 'unsafe-inline' 'unsafe-eval' https://cdn.jsdelivr.net",
      "style-src 'self' 'unsafe-inline' https://fonts.googleapis.com",
      "font-src 'self' https://fonts.gstatic.com",
      "img-src 'self' data: https:",
      "connect-src 'self' https://api.yourpos.com",
      "frame-ancestors 'none'",
      "base-uri 'self'",
      "form-action 'self'"
    ].join('; ');
  }
}
```

### 输入验证组件
```vue
<!-- components/forms/SecureInput.vue -->
<template>
  <div class="secure-input">
    <label v-if="label" :for="inputId" class="input-label">
      {{ label }}
      <span v-if="required" class="required">*</span>
    </label>
    
    <input
      :id="inputId"
      v-model="internalValue"
      :type="inputType"
      :placeholder="placeholder"
      :required="required"
      :maxlength="maxLength"
      :pattern="pattern"
      :class="['input-field', { error: hasError }]"
      @input="handleInput"
      @blur="handleBlur"
    />
    
    <div v-if="hasError" class="error-message">
      {{ errorMessage }}
    </div>
    
    <div v-if="showStrength && type === 'password'" class="password-strength">
      <div class="strength-bar">
        <div 
          :class="['strength-fill', strengthClass]"
          :style="{ width: `${strengthPercentage}%` }"
        ></div>
      </div>
      <span class="strength-text">{{ strengthText }}</span>
    </div>
  </div>
</template>

<script setup lang="ts">
import { SecurityUtils } from '@/utils/security';

interface Props {
  modelValue: string;
  type?: 'text' | 'email' | 'password' | 'tel' | 'url';
  label?: string;
  placeholder?: string;
  required?: boolean;
  maxLength?: number;
  pattern?: string;
  showStrength?: boolean;
  customValidation?: (value: string) => string | null;
}

const props = withDefaults(defineProps<Props>(), {
  type: 'text',
  maxLength: 255,
  showStrength: false
});

const emit = defineEmits<{
  'update:modelValue': [value: string];
  'validation': [isValid: boolean, error?: string];
}>();

const inputId = `input-${Math.random().toString(36).substr(2, 9)}`;
const internalValue = ref(props.modelValue);
const hasError = ref(false);
const errorMessage = ref('');

// 密码强度计算
const passwordStrength = computed(() => {
  if (props.type !== 'password') return 0;
  
  const password = internalValue.value;
  let score = 0;
  
  // 长度检查
  if (password.length >= 8) score += 25;
  if (password.length >= 12) score += 25;
  
  // 复杂度检查
  if (/[a-z]/.test(password)) score += 10;
  if (/[A-Z]/.test(password)) score += 10;
  if (/[0-9]/.test(password)) score += 10;
  if (/[^A-Za-z0-9]/.test(password)) score += 20;
  
  return Math.min(score, 100);
});

const strengthPercentage = computed(() => passwordStrength.value);
const strengthClass = computed(() => {
  const strength = passwordStrength.value;
  if (strength < 30) return 'weak';
  if (strength < 60) return 'medium';
  if (strength < 80) return 'good';
  return 'strong';
});

const strengthText = computed(() => {
  const strength = passwordStrength.value;
  if (strength < 30) return '弱';
  if (strength < 60) return '中等';
  if (strength < 80) return '良好';
  return '强';
});

const inputType = computed(() => {
  return props.type;
});

// 验证函数
const validateInput = (value: string): string | null => {
  // 基础验证
  if (props.required && !value.trim()) {
    return '此字段为必填项';
  }

  if (value.length > props.maxLength) {
    return `输入长度不能超过${props.maxLength}个字符`;
  }

  // 类型特定验证
  switch (props.type) {
    case 'email':
      const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
      if (value && !emailRegex.test(value)) {
        return '请输入有效的邮箱地址';
      }
      break;
      
    case 'password':
      if (value && value.length < 8) {
        return '密码长度至少8个字符';
      }
      break;
      
    case 'url':
      if (value && !SecurityUtils.isValidUrl(value)) {
        return '请输入有效的URL地址';
      }
      break;
  }

  // 自定义验证
  if (props.customValidation) {
    return props.customValidation(value);
  }

  return null;
};

const handleInput = (event: Event) => {
  const target = event.target as HTMLInputElement;
  let value = target.value;
  
  // 安全清理
  value = SecurityUtils.sanitizeInput(value);
  
  internalValue.value = value;
  emit('update:modelValue', value);
  
  // 实时验证
  const error = validateInput(value);
  hasError.value = !!error;
  errorMessage.value = error || '';
  
  emit('validation', !error, error || undefined);
};

const handleBlur = () => {
  const error = validateInput(internalValue.value);
  hasError.value = !!error;
  errorMessage.value = error || '';
  
  emit('validation', !error, error || undefined);
};

// 监听外部值变化
watch(() => props.modelValue, (newValue) => {
  internalValue.value = newValue;
});
</script>

<style scoped>
.secure-input {
  @apply mb-4;
}

.input-label {
  @apply block text-sm font-medium text-gray-700 mb-1;
}

.required {
  @apply text-red-500;
}

.input-field {
  @apply w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500;
}

.input-field.error {
  @apply border-red-500 focus:ring-red-500 focus:border-red-500;
}

.error-message {
  @apply mt-1 text-sm text-red-600;
}

.password-strength {
  @apply mt-2;
}

.strength-bar {
  @apply w-full h-2 bg-gray-200 rounded-full overflow-hidden;
}

.strength-fill {
  @apply h-full transition-all duration-300;
}

.strength-fill.weak {
  @apply bg-red-500;
}

.strength-fill.medium {
  @apply bg-yellow-500;
}

.strength-fill.good {
  @apply bg-blue-500;
}

.strength-fill.strong {
  @apply bg-green-500;
}

.strength-text {
  @apply text-xs text-gray-600 mt-1;
}
</style>
```

## 后端安全

### 认证与授权
```typescript
// backend/src/middleware/auth.ts
import jwt from 'jsonwebtoken';
import bcrypt from 'bcrypt';
import { Request, Response, NextFunction } from 'express';
import { UnauthorizedError } from '@/utils/errors';

export interface AuthRequest extends Request {
  user?: {
    id: number;
    email: string;
    role: string;
  };
}

/**
 * JWT认证中间件
 */
export const authenticateToken = (req: AuthRequest, res: Response, next: NextFunction) => {
  const authHeader = req.headers['authorization'];
  const token = authHeader && authHeader.split(' ')[1];

  if (!token) {
    throw new UnauthorizedError('访问令牌缺失');
  }

  try {
    const decoded = jwt.verify(token, process.env.JWT_SECRET!) as any;
    req.user = decoded;
    next();
  } catch (error) {
    throw new UnauthorizedError('无效的访问令牌');
  }
};

/**
 * 角色权限检查
 */
export const requireRole = (roles: string[]) => {
  return (req: AuthRequest, res: Response, next: NextFunction) => {
    if (!req.user) {
      throw new UnauthorizedError('用户未认证');
    }

    if (!roles.includes(req.user.role)) {
      throw new UnauthorizedError('权限不足');
    }

    next();
  };
};

/**
 * 密码加密工具
 */
export class PasswordUtils {
  private static readonly SALT_ROUNDS = 12;

  /**
   * 加密密码
   */
  static async hashPassword(password: string): Promise<string> {
    return await bcrypt.hash(password, this.SALT_ROUNDS);
  }

  /**
   * 验证密码
   */
  static async verifyPassword(password: string, hash: string): Promise<boolean> {
    return await bcrypt.compare(password, hash);
  }

  /**
   * 密码强度验证
   */
  static validatePasswordStrength(password: string): {
    isValid: boolean;
    errors: string[];
  } {
    const errors: string[] = [];

    if (password.length < 8) {
      errors.push('密码长度至少8个字符');
    }

    if (!/[a-z]/.test(password)) {
      errors.push('密码必须包含小写字母');
    }

    if (!/[A-Z]/.test(password)) {
      errors.push('密码必须包含大写字母');
    }

    if (!/[0-9]/.test(password)) {
      errors.push('密码必须包含数字');
    }

    if (!/[^A-Za-z0-9]/.test(password)) {
      errors.push('密码必须包含特殊字符');
    }

    return {
      isValid: errors.length === 0,
      errors
    };
  }
}

/**
 * JWT工具类
 */
export class JWTUtils {
  /**
   * 生成访问令牌
   */
  static generateAccessToken(payload: object): string {
    return jwt.sign(payload, process.env.JWT_SECRET!, {
      expiresIn: '15m'
    });
  }

  /**
   * 生成刷新令牌
   */
  static generateRefreshToken(payload: object): string {
    return jwt.sign(payload, process.env.JWT_REFRESH_SECRET!, {
      expiresIn: '7d'
    });
  }

  /**
   * 验证刷新令牌
   */
  static verifyRefreshToken(token: string): any {
    return jwt.verify(token, process.env.JWT_REFRESH_SECRET!);
  }
}
```

### 输入验证与清理
```typescript
// backend/src/middleware/validation.ts
import { body, param, query, validationResult } from 'express-validator';
import { Request, Response, NextFunction } from 'express';
import { ValidationError } from '@/utils/errors';
import DOMPurify from 'isomorphic-dompurify';

/**
 * 验证结果处理中间件
 */
export const handleValidationErrors = (req: Request, res: Response, next: NextFunction) => {
  const errors = validationResult(req);
  
  if (!errors.isEmpty()) {
    const errorMessages = errors.array().map(error => error.msg);
    throw new ValidationError(errorMessages.join(', '));
  }
  
  next();
};

/**
 * 输入清理中间件
 */
export const sanitizeInput = (req: Request, res: Response, next: NextFunction) => {
  // 清理请求体
  if (req.body && typeof req.body === 'object') {
    req.body = sanitizeObject(req.body);
  }
  
  // 清理查询参数
  if (req.query && typeof req.query === 'object') {
    req.query = sanitizeObject(req.query);
  }
  
  next();
};

/**
 * 递归清理对象
 */
function sanitizeObject(obj: any): any {
  if (typeof obj === 'string') {
    return DOMPurify.sanitize(obj, { ALLOWED_TAGS: [] });
  }
  
  if (Array.isArray(obj)) {
    return obj.map(sanitizeObject);
  }
  
  if (obj && typeof obj === 'object') {
    const sanitized: any = {};
    for (const [key, value] of Object.entries(obj)) {
      sanitized[key] = sanitizeObject(value);
    }
    return sanitized;
  }
  
  return obj;
}

/**
 * 常用验证规则
 */
export const ValidationRules = {
  // 用户注册验证
  userRegistration: [
    body('email')
      .isEmail()
      .normalizeEmail()
      .withMessage('请输入有效的邮箱地址'),
    body('password')
      .isLength({ min: 8 })
      .withMessage('密码长度至少8个字符')
      .matches(/^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)(?=.*[@$!%*?&])[A-Za-z\d@$!%*?&]/)
      .withMessage('密码必须包含大小写字母、数字和特殊字符'),
    body('name')
      .trim()
      .isLength({ min: 2, max: 50 })
      .withMessage('姓名长度必须在2-50个字符之间')
      .matches(/^[a-zA-Z\u4e00-\u9fa5\s]+$/)
      .withMessage('姓名只能包含字母、汉字和空格')
  ],

  // 产品创建验证
  productCreation: [
    body('name')
      .trim()
      .isLength({ min: 1, max: 255 })
      .withMessage('产品名称不能为空且不超过255个字符'),
    body('slug')
      .trim()
      .isSlug()
      .withMessage('产品别名格式不正确'),
    body('price')
      .isFloat({ min: 0 })
      .withMessage('价格必须为非负数'),
    body('categoryId')
      .isInt({ min: 1 })
      .withMessage('分类ID必须为正整数'),
    body('description')
      .optional()
      .isLength({ max: 5000 })
      .withMessage('描述不能超过5000个字符')
  ],

  // ID参数验证
  idParam: [
    param('id')
      .isInt({ min: 1 })
      .withMessage('ID必须为正整数')
  ],

  // 分页验证
  pagination: [
    query('page')
      .optional()
      .isInt({ min: 1 })
      .withMessage('页码必须为正整数'),
    query('limit')
      .optional()
      .isInt({ min: 1, max: 100 })
      .withMessage('每页数量必须在1-100之间')
  ]
};
```

### SQL注入防护
```typescript
// backend/src/utils/database.ts
import { PrismaClient } from '@prisma/client';

/**
 * 安全的数据库查询工具
 */
export class SafeQuery {
  private prisma: PrismaClient;

  constructor(prisma: PrismaClient) {
    this.prisma = prisma;
  }

  /**
   * 安全的原生查询
   */
  async rawQuery<T>(query: string, params: any[] = []): Promise<T[]> {
    // 使用参数化查询防止SQL注入
    return await this.prisma.$queryRawUnsafe(query, ...params) as T[];
  }

  /**
   * 安全的搜索查询
   */
  async searchProducts(searchTerm: string, categoryId?: number) {
    // 清理搜索词
    const cleanSearchTerm = searchTerm.replace(/[%_]/g, '\\$&');
    
    const where: any = {
      OR: [
        {
          name: {
            contains: cleanSearchTerm,
            mode: 'insensitive'
          }
        },
        {
          description: {
            contains: cleanSearchTerm,
            mode: 'insensitive'
          }
        }
      ]
    };

    if (categoryId) {
      where.categoryId = categoryId;
    }

    return await this.prisma.product.findMany({
      where,
      include: {
        category: true,
        images: true
      }
    });
  }

  /**
   * 安全的批量操作
   */
  async batchUpdate(ids: number[], data: any) {
    // 验证ID数组
    const validIds = ids.filter(id => Number.isInteger(id) && id > 0);
    
    if (validIds.length === 0) {
      throw new Error('无效的ID列表');
    }

    return await this.prisma.product.updateMany({
      where: {
        id: {
          in: validIds
        }
      },
      data
    });
  }
}
```

### 文件上传安全
```typescript
// backend/src/middleware/upload.ts
import multer from 'multer';
import path from 'path';
import crypto from 'crypto';
import { Request } from 'express';
import { ValidationError } from '@/utils/errors';

/**
 * 安全的文件上传配置
 */
const storage = multer.diskStorage({
  destination: (req, file, cb) => {
    const uploadPath = path.join(process.env.UPLOAD_PATH || './uploads', 'images');
    cb(null, uploadPath);
  },
  filename: (req, file, cb) => {
    // 生成安全的文件名
    const ext = path.extname(file.originalname);
    const filename = crypto.randomBytes(16).toString('hex') + ext;
    cb(null, filename);
  }
});

/**
 * 文件类型验证
 */
const fileFilter = (req: Request, file: Express.Multer.File, cb: multer.FileFilterCallback) => {
  // 允许的图片类型
  const allowedTypes = ['image/jpeg', 'image/png', 'image/webp', 'image/gif'];
  
  if (allowedTypes.includes(file.mimetype)) {
    cb(null, true);
  } else {
    cb(new ValidationError('不支持的文件类型'));
  }
};

/**
 * 文件大小限制
 */
const limits = {
  fileSize: 5 * 1024 * 1024, // 5MB
  files: 10 // 最多10个文件
};

export const uploadMiddleware = multer({
  storage,
  fileFilter,
  limits
});

/**
 * 文件安全检查
 */
export class FileSecurityUtils {
  /**
   * 验证文件扩展名
   */
  static validateFileExtension(filename: string): boolean {
    const allowedExtensions = ['.jpg', '.jpeg', '.png', '.webp', '.gif'];
    const ext = path.extname(filename).toLowerCase();
    return allowedExtensions.includes(ext);
  }

  /**
   * 检查文件内容类型
   */
  static async validateFileContent(filePath: string): Promise<boolean> {
    const fs = await import('fs/promises');
    
    try {
      const buffer = await fs.readFile(filePath);
      
      // 检查文件头部字节
      const jpegHeader = buffer.slice(0, 3).toString('hex') === 'ffd8ff';
      const pngHeader = buffer.slice(0, 8).toString('hex') === '89504e470d0a1a0a';
      const webpHeader = buffer.slice(8, 12).toString() === 'WEBP';
      const gifHeader = buffer.slice(0, 6).toString() === 'GIF87a' || buffer.slice(0, 6).toString() === 'GIF89a';
      
      return jpegHeader || pngHeader || webpHeader || gifHeader;
    } catch {
      return false;
    }
  }

  /**
   * 清理文件名
   */
  static sanitizeFilename(filename: string): string {
    return filename
      .replace(/[^a-zA-Z0-9.-]/g, '_')
      .replace(/_{2,}/g, '_')
      .substring(0, 255);
  }
}
```

## 数据保护

### 数据加密
```typescript
// backend/src/utils/encryption.ts
import crypto from 'crypto';

export class EncryptionUtils {
  private static readonly ALGORITHM = 'aes-256-gcm';
  private static readonly KEY_LENGTH = 32;
  private static readonly IV_LENGTH = 16;
  private static readonly TAG_LENGTH = 16;

  /**
   * 生成加密密钥
   */
  static generateKey(): string {
    return crypto.randomBytes(this.KEY_LENGTH).toString('hex');
  }

  /**
   * 加密数据
   */
  static encrypt(text: string, key: string): {
    encrypted: string;
    iv: string;
    tag: string;
  } {
    const iv = crypto.randomBytes(this.IV_LENGTH);
    const cipher = crypto.createCipher(this.ALGORITHM, Buffer.from(key, 'hex'));
    cipher.setAAD(Buffer.from('additional-data'));

    let encrypted = cipher.update(text, 'utf8', 'hex');
    encrypted += cipher.final('hex');

    const tag = cipher.getAuthTag();

    return {
      encrypted,
      iv: iv.toString('hex'),
      tag: tag.toString('hex')
    };
  }

  /**
   * 解密数据
   */
  static decrypt(encryptedData: {
    encrypted: string;
    iv: string;
    tag: string;
  }, key: string): string {
    const decipher = crypto.createDecipher(
      this.ALGORITHM,
      Buffer.from(key, 'hex')
    );

    decipher.setAAD(Buffer.from('additional-data'));
    decipher.setAuthTag(Buffer.from(encryptedData.tag, 'hex'));

    let decrypted = decipher.update(encryptedData.encrypted, 'hex', 'utf8');
    decrypted += decipher.final('utf8');

    return decrypted;
  }

  /**
   * 哈希数据
   */
  static hash(data: string, salt?: string): string {
    const actualSalt = salt || crypto.randomBytes(16).toString('hex');
    const hash = crypto.pbkdf2Sync(data, actualSalt, 10000, 64, 'sha512');
    return `${actualSalt}:${hash.toString('hex')}`;
  }

  /**
   * 验证哈希
   */
  static verifyHash(data: string, hash: string): boolean {
    const [salt, originalHash] = hash.split(':');
    const newHash = crypto.pbkdf2Sync(data, salt, 10000, 64, 'sha512');
    return originalHash === newHash.toString('hex');
  }
}
```

### 敏感数据处理
```typescript
// backend/src/utils/dataProtection.ts
export class DataProtectionUtils {
  /**
   * 脱敏手机号
   */
  static maskPhone(phone: string): string {
    if (!phone || phone.length < 7) return phone;
    return phone.replace(/(\d{3})\d{4}(\d{4})/, '$1****$2');
  }

  /**
   * 脱敏邮箱
   */
  static maskEmail(email: string): string {
    if (!email || !email.includes('@')) return email;
    const [username, domain] = email.split('@');
    const maskedUsername = username.length > 2 
      ? username.substring(0, 2) + '*'.repeat(username.length - 2)
      : username;
    return `${maskedUsername}@${domain}`;
  }

  /**
   * 脱敏身份证号
   */
  static maskIdCard(idCard: string): string {
    if (!idCard || idCard.length < 8) return idCard;
    return idCard.replace(/(\d{4})\d{10}(\d{4})/, '$1**********$2');
  }

  /**
   * 脱敏银行卡号
   */
  static maskBankCard(cardNumber: string): string {
    if (!cardNumber || cardNumber.length < 8) return cardNumber;
    return cardNumber.replace(/(\d{4})\d+(\d{4})/, '$1****$2');
  }

  /**
   * 检查是否包含敏感信息
   */
  static containsSensitiveData(text: string): boolean {
    const patterns = [
      /\d{15}|\d{18}/, // 身份证号
      /\d{16,19}/, // 银行卡号
      /1[3-9]\d{9}/, // 手机号
      /\b[A-Za-z0-9._%+-]+@[A-Za-z0-9.-]+\.[A-Z|a-z]{2,}\b/ // 邮箱
    ];

    return patterns.some(pattern => pattern.test(text));
  }

  /**
   * 清理敏感数据
   */
  static sanitizeSensitiveData(obj: any): any {
    if (typeof obj === 'string') {
      if (this.containsSensitiveData(obj)) {
        return '[敏感数据已隐藏]';
      }
      return obj;
    }

    if (Array.isArray(obj)) {
      return obj.map(item => this.sanitizeSensitiveData(item));
    }

    if (obj && typeof obj === 'object') {
      const sanitized: any = {};
      for (const [key, value] of Object.entries(obj)) {
        // 特定字段脱敏
        if (key.toLowerCase().includes('phone')) {
          sanitized[key] = this.maskPhone(value as string);
        } else if (key.toLowerCase().includes('email')) {
          sanitized[key] = this.maskEmail(value as string);
        } else if (key.toLowerCase().includes('idcard')) {
          sanitized[key] = this.maskIdCard(value as string);
        } else {
          sanitized[key] = this.sanitizeSensitiveData(value);
        }
      }
      return sanitized;
    }

    return obj;
  }
}
```

## 安全监控

### 安全日志记录
```typescript
// backend/src/middleware/securityLogger.ts
import { Request, Response, NextFunction } from 'express';
import { logger } from '@/utils/logger';

interface SecurityEvent {
  type: 'login' | 'logout' | 'failed_login' | 'permission_denied' | 'suspicious_activity';
  userId?: number;
  ip: string;
  userAgent: string;
  details?: any;
  timestamp: Date;
}

/**
 * 安全事件记录器
 */
export class SecurityLogger {
  /**
   * 记录安全事件
   */
  static logSecurityEvent(event: SecurityEvent) {
    logger.warn('安全事件', {
      ...event,
      level: 'security'
    });

    // 如果是可疑活动，发送告警
    if (event.type === 'suspicious_activity') {
      this.sendSecurityAlert(event);
    }
  }

  /**
   * 发送安全告警
   */
  private static async sendSecurityAlert(event: SecurityEvent) {
    // 这里可以集成邮件、短信或其他告警系统
    console.warn('安全告警:', event);
  }

  /**
   * 检测可疑活动
   */
  static detectSuspiciousActivity(req: Request): boolean {
    const suspiciousPatterns = [
      /union.*select/i,
      /script.*alert/i,
      /<script/i,
      /javascript:/i,
      /eval\(/i,
      /document\.cookie/i
    ];

    const requestString = JSON.stringify({
      url: req.url,
      body: req.body,
      query: req.query,
      headers: req.headers
    });

    return suspiciousPatterns.some(pattern => pattern.test(requestString));
  }
}

/**
 * 安全监控中间件
 */
export const securityMonitoringMiddleware = (req: Request, res: Response, next: NextFunction) => {
  // 检测可疑活动
  if (SecurityLogger.detectSuspiciousActivity(req)) {
    SecurityLogger.logSecurityEvent({
      type: 'suspicious_activity',
      ip: req.ip,
      userAgent: req.get('User-Agent') || '',
      details: {
        url: req.url,
        method: req.method,
        body: req.body,
        query: req.query
      },
      timestamp: new Date()
    });
  }

  // 记录登录尝试
  if (req.path === '/api/auth/login') {
    res.on('finish', () => {
      const eventType = res.statusCode === 200 ? 'login' : 'failed_login';
      SecurityLogger.logSecurityEvent({
        type: eventType,
        ip: req.ip,
        userAgent: req.get('User-Agent') || '',
        details: {
          email: req.body.email
        },
        timestamp: new Date()
      });
    });
  }

  next();
};
```

### 速率限制
```typescript
// backend/src/middleware/rateLimiter.ts
import rateLimit from 'express-rate-limit';
import RedisStore from 'rate-limit-redis';
import Redis from 'ioredis';

const redis = new Redis({
  host: process.env.REDIS_HOST || 'localhost',
  port: parseInt(process.env.REDIS_PORT || '6379')
});

/**
 * 通用速率限制
 */
export const generalLimiter = rateLimit({
  store: new RedisStore({
    sendCommand: (...args: string[]) => redis.call(...args)
  }),
  windowMs: 15 * 60 * 1000, // 15分钟
  max: 100, // 限制每个IP 15分钟内最多100个请求
  message: {
    error: '请求过于频繁，请稍后再试'
  },
  standardHeaders: true,
  legacyHeaders: false
});

/**
 * 登录速率限制
 */
export const loginLimiter = rateLimit({
  store: new RedisStore({
    sendCommand: (...args: string[]) => redis.call(...args)
  }),
  windowMs: 15 * 60 * 1000, // 15分钟
  max: 5, // 限制每个IP 15分钟内最多5次登录尝试
  message: {
    error: '登录尝试次数过多，请15分钟后再试'
  },
  skipSuccessfulRequests: true
});

/**
 * API速率限制
 */
export const apiLimiter = rateLimit({
  store: new RedisStore({
    sendCommand: (...args: string[]) => redis.call(...args)
  }),
  windowMs: 60 * 1000, // 1分钟
  max: 60, // 限制每个IP每分钟最多60个API请求
  message: {
    error: 'API请求过于频繁，请稍后再试'
  }
});

/**
 * 文件上传速率限制
 */
export const uploadLimiter = rateLimit({
  store: new RedisStore({
    sendCommand: (...args: string[]) => redis.call(...args)
  }),
  windowMs: 60 * 60 * 1000, // 1小时
  max: 10, // 限制每个IP每小时最多10次文件上传
  message: {
    error: '文件上传过于频繁，请稍后再试'
  }
});
```

