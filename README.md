# 企业POS机产品展示网站

这是一个现代化的企业POS机产品展示网站，采用最新的技术栈开发，具备SEO优化、管理后台、一键安装等功能。

## 技术栈

### 前端（企业官网）
- **框架**: Nuxt.js 3 + Vue 3 + TypeScript
- **样式**: TailwindCSS
- **状态管理**: Pinia
- **SEO优化**: Nuxt SEO 模块
- **图片优化**: Nuxt Image 模块

### 后端（API服务）
- **运行时**: Node.js + Express + TypeScript
- **数据库**: MySQL 5.7 + Prisma ORM
- **认证**: JWT + bcrypt
- **日志**: Winston
- **文件上传**: Multer

### 管理后台
- **框架**: Vue 3 + Element Plus + Vite
- **路由**: Vue Router 4
- **状态管理**: Pinia
- **HTTP客户端**: Axios

### 部署环境
- **服务器**: Rocky Linux 9.5
- **Web服务器**: Nginx
- **数据库**: MySQL 5.7
- **进程管理**: PM2
- **面板**: 宝塔LNMP环境

## 项目特性

1. **SEO友好** - 完整的SEO优化方案
2. **现代化UI** - 响应式设计，用户体验优秀
3. **管理后台** - 完整的内容管理系统
4. **一键安装** - 类似WordPress的安装体验
5. **高性能** - 多层缓存，性能优化
6. **用户统计** - 完整的访问分析功能

## 快速开始

### 环境要求
- Node.js 18+
- MySQL 5.7+
- pnpm (推荐)

### 安装步骤

1. 克隆项目
```bash
git clone https://github.com/your-username/pos-website.git
cd pos-website
```

2. 安装依赖
```bash
# 前端
cd frontend && pnpm install

# 后端
cd ../backend && pnpm install

# 管理后台
cd ../admin && pnpm install
```

3. 配置环境变量
```bash
cp .env.example .env
# 编辑 .env 文件配置数据库等信息
```

4. 初始化数据库
```bash
cd backend
pnpm prisma migrate dev
pnpm prisma db seed
```

5. 启动开发服务器
```bash
# 后端API (端口3001)
cd backend && pnpm dev

# 前端网站 (端口3000)
cd frontend && pnpm dev

# 管理后台 (端口3002)
cd admin && pnpm dev
```

## 项目结构

```
pos-website/
├── frontend/          # Nuxt.js 前端项目
├── backend/           # Express 后端API
├── admin/             # Vue 管理后台
├── installer/         # 一键安装向导
├── deployment/        # 部署配置
├── shared/            # 共享代码
└── docs/              # 项目文档
```

## 部署

### 生产环境部署

1. 使用一键安装向导
```bash
# 访问 http://your-domain.com/install
```

2. 手动部署
```bash
# 构建项目
pnpm build:all

# 使用PM2启动
pm2 start ecosystem.config.js
```

详细部署说明请参考 [部署指南](docs/05-deployment-guide.mdc)

## 文档

- [项目概述](docs/01-project-overview.mdc)
- [项目结构](docs/02-project-structure.mdc)
- [开发规范](docs/03-development-standards.mdc)
- [SEO优化](docs/04-seo-optimization.mdc)
- [部署指南](docs/05-deployment-guide.mdc)
- [性能优化](docs/06-performance-optimization.mdc)
- [安全指南](docs/07-security-guidelines.mdc)
- [数据库设计](docs/08-database-design.mdc)

## 贡献

欢迎提交 Issue 和 Pull Request 来改进这个项目。

## 许可证

MIT License
