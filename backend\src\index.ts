/**
 * 后端API服务主入口文件
 */

import express from 'express';
import cors from 'cors';
import helmet from 'helmet';
import compression from 'compression';
import morgan from 'morgan';
import cookieParser from 'cookie-parser';
import rateLimit from 'express-rate-limit';
import { config } from './config';
import { logger } from './utils/logger';
import { DatabaseManager } from './config/database';
import { errorHandler } from './middleware/errorHandler';
import { notFoundHandler } from './middleware/notFoundHandler';
import { authMiddleware } from './middleware/auth';

// 导入路由
import authRoutes from './routes/auth.routes';
import productRoutes from './routes/product.routes';
import categoryRoutes from './routes/category.routes';
import articleRoutes from './routes/article.routes';
import companyRoutes from './routes/company.routes';
import contactRoutes from './routes/contact.routes';
import uploadRoutes from './routes/upload.routes';
import analyticsRoutes from './routes/analytics.routes';
import settingRoutes from './routes/setting.routes';

/**
 * 创建Express应用
 */
function createApp(): express.Application {
  const app = express();

  // 安全中间件
  app.use(helmet({
    crossOriginEmbedderPolicy: false,
    contentSecurityPolicy: {
      directives: {
        defaultSrc: ["'self'"],
        styleSrc: ["'self'", "'unsafe-inline'"],
        scriptSrc: ["'self'"],
        imgSrc: ["'self'", "data:", "https:"],
      },
    },
  }));

  // CORS配置
  app.use(cors({
    origin: config.cors.origin,
    credentials: true,
    methods: ['GET', 'POST', 'PUT', 'DELETE', 'PATCH', 'OPTIONS'],
    allowedHeaders: ['Content-Type', 'Authorization', 'X-Requested-With'],
  }));

  // 压缩响应
  app.use(compression());

  // 请求日志
  app.use(morgan('combined', {
    stream: {
      write: (message: string) => {
        logger.info(message.trim());
      }
    }
  }));

  // 解析请求体
  app.use(express.json({ limit: '10mb' }));
  app.use(express.urlencoded({ extended: true, limit: '10mb' }));
  app.use(cookieParser());

  // 速率限制
  const limiter = rateLimit({
    windowMs: config.rateLimit.windowMs,
    max: config.rateLimit.maxRequests,
    message: {
      error: '请求过于频繁，请稍后再试'
    },
    standardHeaders: true,
    legacyHeaders: false,
  });
  app.use('/api', limiter);

  // 健康检查
  app.get('/health', (req, res) => {
    res.json({
      status: 'ok',
      timestamp: new Date().toISOString(),
      uptime: process.uptime(),
      environment: config.env,
      version: process.env.npm_package_version || '1.0.0'
    });
  });

  // API路由
  app.use('/api/auth', authRoutes);
  app.use('/api/products', productRoutes);
  app.use('/api/categories', categoryRoutes);
  app.use('/api/articles', articleRoutes);
  app.use('/api/company', companyRoutes);
  app.use('/api/contact', contactRoutes);
  app.use('/api/upload', authMiddleware, uploadRoutes);
  app.use('/api/analytics', authMiddleware, analyticsRoutes);
  app.use('/api/settings', authMiddleware, settingRoutes);

  // 静态文件服务
  app.use('/uploads', express.static(config.upload.path));

  // API文档
  if (config.env === 'development') {
    import('./docs/swagger').then(({ setupSwagger }) => {
      setupSwagger(app);
    });
  }

  // 404处理
  app.use(notFoundHandler);

  // 错误处理
  app.use(errorHandler);

  return app;
}

/**
 * 启动服务器
 */
async function startServer(): Promise<void> {
  try {
    // 连接数据库
    const dbManager = DatabaseManager.getInstance();
    await dbManager.connect();

    // 创建应用
    const app = createApp();

    // 启动服务器
    const server = app.listen(config.port, config.host, () => {
      logger.info(`🚀 服务器启动成功`);
      logger.info(`📍 地址: http://${config.host}:${config.port}`);
      logger.info(`🌍 环境: ${config.env}`);
      logger.info(`📚 API文档: http://${config.host}:${config.port}/api-docs`);
    });

    // 优雅关闭
    const gracefulShutdown = async (signal: string) => {
      logger.info(`收到 ${signal} 信号，开始优雅关闭...`);
      
      server.close(async () => {
        logger.info('HTTP服务器已关闭');
        
        try {
          await dbManager.disconnect();
          logger.info('数据库连接已关闭');
          process.exit(0);
        } catch (error) {
          logger.error('关闭数据库连接时出错:', error);
          process.exit(1);
        }
      });

      // 强制关闭超时
      setTimeout(() => {
        logger.error('强制关闭服务器');
        process.exit(1);
      }, 10000);
    };

    // 监听关闭信号
    process.on('SIGTERM', () => gracefulShutdown('SIGTERM'));
    process.on('SIGINT', () => gracefulShutdown('SIGINT'));

    // 监听未捕获的异常
    process.on('uncaughtException', (error) => {
      logger.error('未捕获的异常:', error);
      process.exit(1);
    });

    process.on('unhandledRejection', (reason, promise) => {
      logger.error('未处理的Promise拒绝:', reason);
      logger.error('Promise:', promise);
      process.exit(1);
    });

  } catch (error) {
    logger.error('启动服务器失败:', error);
    process.exit(1);
  }
}

// 启动应用
if (require.main === module) {
  startServer();
}

export { createApp };
