/**
 * 认证状态管理
 */

import { defineStore } from 'pinia'
import type { IUser } from '@/shared/types'

interface AuthState {
  user: IUser | null
  accessToken: string | null
  refreshToken: string | null
  isAuthenticated: boolean
  isLoading: boolean
}

export const useAuthStore = defineStore('auth', {
  state: (): AuthState => ({
    user: null,
    accessToken: null,
    refreshToken: null,
    isAuthenticated: false,
    isLoading: false
  }),

  getters: {
    /**
     * 是否为管理员
     */
    isAdmin: (state): boolean => {
      return state.user?.role === 'ADMIN'
    },

    /**
     * 是否为编辑者
     */
    isEditor: (state): boolean => {
      return state.user?.role === 'EDITOR' || state.user?.role === 'ADMIN'
    },

    /**
     * 用户显示名称
     */
    displayName: (state): string => {
      return state.user?.name || '未知用户'
    },

    /**
     * 用户头像
     */
    avatar: (state): string => {
      return state.user?.avatar || '/images/default-avatar.png'
    }
  },

  actions: {
    /**
     * 登录
     */
    async login(email: string, password: string): Promise<void> {
      this.isLoading = true
      
      try {
        const authApi = useAuthApi()
        const response = await authApi.login(email, password)
        
        if (response.success && response.data) {
          this.setAuthData(response.data)
          
          // 保存到Cookie
          const accessTokenCookie = useCookie('accessToken', {
            maxAge: 60 * 60 * 24 * 7, // 7天
            httpOnly: false,
            secure: true,
            sameSite: 'strict'
          })
          
          const refreshTokenCookie = useCookie('refreshToken', {
            maxAge: 60 * 60 * 24 * 30, // 30天
            httpOnly: false,
            secure: true,
            sameSite: 'strict'
          })
          
          accessTokenCookie.value = response.data.accessToken
          refreshTokenCookie.value = response.data.refreshToken
          
          // 跳转到首页或之前的页面
          const router = useRouter()
          const route = useRoute()
          const redirectTo = route.query.redirect as string || '/'
          await router.push(redirectTo)
        }
      } catch (error) {
        console.error('Login failed:', error)
        throw error
      } finally {
        this.isLoading = false
      }
    },

    /**
     * 注册
     */
    async register(data: { email: string; password: string; name: string }): Promise<void> {
      this.isLoading = true
      
      try {
        const authApi = useAuthApi()
        const response = await authApi.register(data)
        
        if (response.success) {
          // 注册成功后自动登录
          await this.login(data.email, data.password)
        }
      } catch (error) {
        console.error('Registration failed:', error)
        throw error
      } finally {
        this.isLoading = false
      }
    },

    /**
     * 登出
     */
    async logout(): Promise<void> {
      try {
        const authApi = useAuthApi()
        await authApi.logout()
      } catch (error) {
        console.error('Logout failed:', error)
      } finally {
        this.clearAuthData()
        
        // 清除Cookie
        const accessTokenCookie = useCookie('accessToken')
        const refreshTokenCookie = useCookie('refreshToken')
        accessTokenCookie.value = null
        refreshTokenCookie.value = null
        
        // 跳转到首页
        const router = useRouter()
        await router.push('/')
      }
    },

    /**
     * 刷新令牌
     */
    async refreshAccessToken(): Promise<boolean> {
      if (!this.refreshToken) {
        return false
      }
      
      try {
        const authApi = useAuthApi()
        const response = await authApi.refreshToken(this.refreshToken)
        
        if (response.success && response.data) {
          this.accessToken = response.data.accessToken
          this.user = response.data.user
          
          // 更新Cookie
          const accessTokenCookie = useCookie('accessToken')
          accessTokenCookie.value = response.data.accessToken
          
          return true
        }
      } catch (error) {
        console.error('Token refresh failed:', error)
        this.clearAuthData()
      }
      
      return false
    },

    /**
     * 获取用户信息
     */
    async fetchProfile(): Promise<void> {
      if (!this.accessToken) {
        return
      }
      
      try {
        const authApi = useAuthApi()
        const response = await authApi.getProfile()
        
        if (response.success && response.data) {
          this.user = response.data
        }
      } catch (error) {
        console.error('Fetch profile failed:', error)
        // 如果获取用户信息失败，可能是令牌过期
        const refreshed = await this.refreshAccessToken()
        if (!refreshed) {
          this.clearAuthData()
        }
      }
    },

    /**
     * 初始化认证状态
     */
    async initAuth(): Promise<void> {
      // 从Cookie中恢复认证信息
      const accessTokenCookie = useCookie('accessToken')
      const refreshTokenCookie = useCookie('refreshToken')
      
      if (accessTokenCookie.value && refreshTokenCookie.value) {
        this.accessToken = accessTokenCookie.value
        this.refreshToken = refreshTokenCookie.value
        this.isAuthenticated = true
        
        // 获取用户信息
        await this.fetchProfile()
      }
    },

    /**
     * 设置认证数据
     */
    setAuthData(data: {
      user: IUser
      accessToken: string
      refreshToken: string
    }): void {
      this.user = data.user
      this.accessToken = data.accessToken
      this.refreshToken = data.refreshToken
      this.isAuthenticated = true
    },

    /**
     * 清除认证数据
     */
    clearAuthData(): void {
      this.user = null
      this.accessToken = null
      this.refreshToken = null
      this.isAuthenticated = false
    },

    /**
     * 检查权限
     */
    hasPermission(permission: string): boolean {
      if (!this.user) {
        return false
      }
      
      // 管理员拥有所有权限
      if (this.user.role === 'ADMIN') {
        return true
      }
      
      // 这里可以根据具体的权限系统实现
      // 目前简化为基于角色的权限检查
      const rolePermissions: Record<string, string[]> = {
        ADMIN: ['*'], // 所有权限
        EDITOR: [
          'products.read',
          'products.write',
          'categories.read',
          'categories.write',
          'articles.read',
          'articles.write'
        ],
        USER: [
          'products.read',
          'articles.read'
        ]
      }
      
      const userPermissions = rolePermissions[this.user.role] || []
      return userPermissions.includes('*') || userPermissions.includes(permission)
    },

    /**
     * 检查是否可以访问路由
     */
    canAccessRoute(routePath: string): boolean {
      // 公开路由
      const publicRoutes = ['/', '/products', '/about', '/contact', '/news', '/login', '/register']
      
      if (publicRoutes.some(route => routePath.startsWith(route))) {
        return true
      }
      
      // 需要登录的路由
      if (!this.isAuthenticated) {
        return false
      }
      
      // 管理员路由
      if (routePath.startsWith('/admin')) {
        return this.isAdmin
      }
      
      return true
    }
  }
})
