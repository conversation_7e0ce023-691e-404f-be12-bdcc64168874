<template>
  <section class="relative bg-gradient-hero overflow-hidden">
    <!-- 背景装饰 -->
    <div class="absolute inset-0">
      <div class="absolute inset-0 bg-black opacity-20"></div>
      <div class="absolute top-0 left-0 w-full h-full">
        <div class="absolute top-10 left-10 w-20 h-20 bg-white opacity-10 rounded-full animate-pulse"></div>
        <div class="absolute top-32 right-20 w-16 h-16 bg-white opacity-10 rounded-full animate-pulse delay-1000"></div>
        <div class="absolute bottom-20 left-1/4 w-12 h-12 bg-white opacity-10 rounded-full animate-pulse delay-2000"></div>
      </div>
    </div>

    <div class="relative container py-20 lg:py-32">
      <div class="grid grid-cols-1 lg:grid-cols-2 gap-12 items-center">
        <!-- 左侧内容 -->
        <div class="text-white space-y-8">
          <div class="space-y-4">
            <h1 class="text-4xl lg:text-6xl font-bold leading-tight">
              专业的
              <span class="text-yellow-400">POS机</span>
              产品展示平台
            </h1>
            <p class="text-xl lg:text-2xl text-gray-200 leading-relaxed">
              为您提供最新最全的支付终端设备信息，助力商户数字化转型
            </p>
          </div>

          <!-- 特色标签 -->
          <div class="flex flex-wrap gap-3">
            <span
              v-for="feature in features"
              :key="feature"
              class="px-4 py-2 bg-white bg-opacity-20 rounded-full text-sm font-medium backdrop-blur-sm"
            >
              {{ feature }}
            </span>
          </div>

          <!-- 行动按钮 -->
          <div class="flex flex-col sm:flex-row gap-4">
            <NuxtLink
              to="/products"
              class="btn btn-lg bg-white text-blue-600 hover:bg-gray-100 font-semibold px-8 py-4 rounded-lg transition-all duration-300 transform hover:scale-105 shadow-lg"
            >
              浏览产品
              <Icon name="heroicons:arrow-right" class="w-5 h-5 ml-2" />
            </NuxtLink>
            <NuxtLink
              to="/contact"
              class="btn btn-lg btn-outline border-white text-white hover:bg-white hover:text-blue-600 font-semibold px-8 py-4 rounded-lg transition-all duration-300"
            >
              联系咨询
            </NuxtLink>
          </div>

          <!-- 统计数据 -->
          <div class="grid grid-cols-3 gap-6 pt-8">
            <div
              v-for="stat in statistics"
              :key="stat.label"
              class="text-center"
            >
              <div class="text-3xl lg:text-4xl font-bold text-yellow-400">
                {{ stat.value }}
              </div>
              <div class="text-sm text-gray-300 mt-1">
                {{ stat.label }}
              </div>
            </div>
          </div>
        </div>

        <!-- 右侧图片 -->
        <div class="relative">
          <!-- 主要产品图片 -->
          <div class="relative z-10">
            <NuxtImg
              src="/images/hero-pos-device.png"
              alt="POS机设备"
              width="600"
              height="400"
              class="w-full h-auto drop-shadow-2xl"
              loading="eager"
              preload
            />
          </div>

          <!-- 浮动卡片 -->
          <div class="absolute top-4 -left-4 z-20 animate-float">
            <div class="bg-white rounded-lg shadow-xl p-4 max-w-xs">
              <div class="flex items-center space-x-3">
                <div class="w-12 h-12 bg-green-100 rounded-full flex items-center justify-center">
                  <Icon name="heroicons:check-circle" class="w-6 h-6 text-green-600" />
                </div>
                <div>
                  <div class="font-semibold text-gray-900">安全支付</div>
                  <div class="text-sm text-gray-600">银联认证</div>
                </div>
              </div>
            </div>
          </div>

          <div class="absolute bottom-4 -right-4 z-20 animate-float delay-1000">
            <div class="bg-white rounded-lg shadow-xl p-4 max-w-xs">
              <div class="flex items-center space-x-3">
                <div class="w-12 h-12 bg-blue-100 rounded-full flex items-center justify-center">
                  <Icon name="heroicons:signal" class="w-6 h-6 text-blue-600" />
                </div>
                <div>
                  <div class="font-semibold text-gray-900">快速连接</div>
                  <div class="text-sm text-gray-600">4G/WiFi双模</div>
                </div>
              </div>
            </div>
          </div>

          <!-- 背景装饰圆圈 -->
          <div class="absolute -top-8 -right-8 w-32 h-32 bg-white opacity-10 rounded-full"></div>
          <div class="absolute -bottom-8 -left-8 w-24 h-24 bg-white opacity-10 rounded-full"></div>
        </div>
      </div>
    </div>

    <!-- 滚动指示器 -->
    <div class="absolute bottom-8 left-1/2 transform -translate-x-1/2">
      <div class="animate-bounce">
        <Icon name="heroicons:chevron-down" class="w-6 h-6 text-white opacity-70" />
      </div>
    </div>
  </section>
</template>

<script setup lang="ts">
// 特色功能
const features = [
  '银联认证',
  '多种支付方式',
  '云端管理',
  '7x24技术支持'
]

// 统计数据
const statistics = [
  { value: '1000+', label: '合作商户' },
  { value: '50+', label: '产品型号' },
  { value: '99.9%', label: '系统稳定性' }
]
</script>

<style scoped>
@keyframes float {
  0%, 100% {
    transform: translateY(0px);
  }
  50% {
    transform: translateY(-10px);
  }
}

.animate-float {
  animation: float 3s ease-in-out infinite;
}

.delay-1000 {
  animation-delay: 1s;
}
</style>
