/**
 * 文件上传路由
 */

import { Router } from 'express';
import { UploadController } from '../controllers/upload.controller';
import { requireEditor } from '../middleware/auth';
import { asyncHandler } from '../middleware/errorHandler';
import { uploadMiddleware } from '../middleware/upload';

const router = Router();
const uploadController = new UploadController();

/**
 * @swagger
 * /api/upload/image:
 *   post:
 *     summary: 上传图片
 *     tags: [文件上传]
 *     security:
 *       - bearerAuth: []
 *     requestBody:
 *       required: true
 *       content:
 *         multipart/form-data:
 *           schema:
 *             type: object
 *             properties:
 *               file:
 *                 type: string
 *                 format: binary
 *                 description: 图片文件
 *     responses:
 *       200:
 *         description: 上传成功
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                 data:
 *                   type: object
 *                   properties:
 *                     filename:
 *                       type: string
 *                       description: 文件名
 *                     originalName:
 *                       type: string
 *                       description: 原始文件名
 *                     mimetype:
 *                       type: string
 *                       description: 文件类型
 *                     size:
 *                       type: number
 *                       description: 文件大小
 *                     url:
 *                       type: string
 *                       description: 文件访问URL
 *       400:
 *         description: 文件格式不支持或文件过大
 *       401:
 *         description: 未授权
 *       403:
 *         description: 权限不足
 */
router.post('/image',
  requireEditor,
  uploadMiddleware.single('file'),
  asyncHandler(uploadController.uploadImage.bind(uploadController))
);

/**
 * @swagger
 * /api/upload/file:
 *   post:
 *     summary: 上传文件
 *     tags: [文件上传]
 *     security:
 *       - bearerAuth: []
 *     requestBody:
 *       required: true
 *       content:
 *         multipart/form-data:
 *           schema:
 *             type: object
 *             properties:
 *               file:
 *                 type: string
 *                 format: binary
 *                 description: 文件
 *     responses:
 *       200:
 *         description: 上传成功
 *       400:
 *         description: 文件格式不支持或文件过大
 *       401:
 *         description: 未授权
 *       403:
 *         description: 权限不足
 */
router.post('/file',
  requireEditor,
  uploadMiddleware.single('file'),
  asyncHandler(uploadController.uploadFile.bind(uploadController))
);

/**
 * @swagger
 * /api/upload/multiple:
 *   post:
 *     summary: 批量上传图片
 *     tags: [文件上传]
 *     security:
 *       - bearerAuth: []
 *     requestBody:
 *       required: true
 *       content:
 *         multipart/form-data:
 *           schema:
 *             type: object
 *             properties:
 *               files:
 *                 type: array
 *                 items:
 *                   type: string
 *                   format: binary
 *                 description: 图片文件数组
 *     responses:
 *       200:
 *         description: 上传成功
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                 data:
 *                   type: array
 *                   items:
 *                     type: object
 *                     properties:
 *                       filename:
 *                         type: string
 *                       originalName:
 *                         type: string
 *                       mimetype:
 *                         type: string
 *                       size:
 *                         type: number
 *                       url:
 *                         type: string
 *       400:
 *         description: 文件格式不支持或文件过大
 *       401:
 *         description: 未授权
 *       403:
 *         description: 权限不足
 */
router.post('/multiple',
  requireEditor,
  uploadMiddleware.array('files', 10),
  asyncHandler(uploadController.uploadMultiple.bind(uploadController))
);

export default router;
