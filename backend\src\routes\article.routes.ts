/**
 * 文章路由
 */

import { Router } from 'express';
import { body, query } from 'express-validator';
import { ArticleController } from '../controllers/article.controller';
import { authMiddleware, requireEditor } from '../middleware/auth';
import { asyncHandler } from '../middleware/errorHandler';
import { validateRequest } from '../middleware/validation';

const router = Router();
const articleController = new ArticleController();

/**
 * 文章创建验证规则
 */
const createArticleValidation = [
  body('title')
    .trim()
    .isLength({ min: 2, max: 255 })
    .withMessage('文章标题长度必须在2-255个字符之间'),
  body('slug')
    .trim()
    .isLength({ min: 2, max: 255 })
    .matches(/^[a-z0-9]+(?:-[a-z0-9]+)*$/)
    .withMessage('文章标识符格式不正确'),
  body('content')
    .trim()
    .isLength({ min: 10 })
    .withMessage('文章内容至少需要10个字符'),
  body('excerpt')
    .optional()
    .isLength({ max: 500 })
    .withMessage('文章摘要不能超过500个字符'),
  body('categoryId')
    .optional()
    .isInt({ min: 1 })
    .withMessage('分类ID必须是正整数'),
];

/**
 * 文章更新验证规则
 */
const updateArticleValidation = [
  body('title')
    .optional()
    .trim()
    .isLength({ min: 2, max: 255 })
    .withMessage('文章标题长度必须在2-255个字符之间'),
  body('slug')
    .optional()
    .trim()
    .isLength({ min: 2, max: 255 })
    .matches(/^[a-z0-9]+(?:-[a-z0-9]+)*$/)
    .withMessage('文章标识符格式不正确'),
  body('content')
    .optional()
    .trim()
    .isLength({ min: 10 })
    .withMessage('文章内容至少需要10个字符'),
  body('excerpt')
    .optional()
    .isLength({ max: 500 })
    .withMessage('文章摘要不能超过500个字符'),
  body('categoryId')
    .optional()
    .isInt({ min: 1 })
    .withMessage('分类ID必须是正整数'),
];

/**
 * 查询验证规则
 */
const queryValidation = [
  query('page')
    .optional()
    .isInt({ min: 1 })
    .withMessage('页码必须是正整数'),
  query('limit')
    .optional()
    .isInt({ min: 1, max: 100 })
    .withMessage('每页数量必须在1-100之间'),
];

/**
 * @swagger
 * /api/articles:
 *   get:
 *     summary: 获取文章列表
 *     tags: [文章]
 *     parameters:
 *       - in: query
 *         name: page
 *         schema:
 *           type: integer
 *           minimum: 1
 *         description: 页码
 *       - in: query
 *         name: limit
 *         schema:
 *           type: integer
 *           minimum: 1
 *           maximum: 100
 *         description: 每页数量
 *       - in: query
 *         name: category
 *         schema:
 *           type: string
 *         description: 分类标识符
 *       - in: query
 *         name: status
 *         schema:
 *           type: string
 *           enum: [DRAFT, PUBLISHED, ARCHIVED]
 *         description: 文章状态
 *       - in: query
 *         name: search
 *         schema:
 *           type: string
 *         description: 搜索关键词
 *       - in: query
 *         name: sortBy
 *         schema:
 *           type: string
 *           enum: [created, updated, published, title]
 *         description: 排序字段
 *       - in: query
 *         name: sortOrder
 *         schema:
 *           type: string
 *           enum: [asc, desc]
 *         description: 排序方向
 *     responses:
 *       200:
 *         description: 获取成功
 */
router.get('/',
  queryValidation,
  validateRequest,
  asyncHandler(articleController.getArticles.bind(articleController))
);

/**
 * @swagger
 * /api/articles:
 *   post:
 *     summary: 创建文章
 *     tags: [文章]
 *     security:
 *       - bearerAuth: []
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             $ref: '#/components/schemas/CreateArticleDto'
 *     responses:
 *       201:
 *         description: 创建成功
 *       400:
 *         description: 请求参数错误
 *       401:
 *         description: 未授权
 *       403:
 *         description: 权限不足
 */
router.post('/',
  authMiddleware,
  requireEditor,
  createArticleValidation,
  validateRequest,
  asyncHandler(articleController.createArticle.bind(articleController))
);

/**
 * @swagger
 * /api/articles/{slug}:
 *   get:
 *     summary: 获取文章详情
 *     tags: [文章]
 *     parameters:
 *       - in: path
 *         name: slug
 *         required: true
 *         schema:
 *           type: string
 *         description: 文章标识符
 *     responses:
 *       200:
 *         description: 获取成功
 *       404:
 *         description: 文章不存在
 */
router.get('/:slug',
  asyncHandler(articleController.getArticle.bind(articleController))
);

/**
 * @swagger
 * /api/articles/{id}:
 *   put:
 *     summary: 更新文章
 *     tags: [文章]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: id
 *         required: true
 *         schema:
 *           type: integer
 *         description: 文章ID
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             $ref: '#/components/schemas/UpdateArticleDto'
 *     responses:
 *       200:
 *         description: 更新成功
 *       400:
 *         description: 请求参数错误
 *       401:
 *         description: 未授权
 *       403:
 *         description: 权限不足
 *       404:
 *         description: 文章不存在
 */
router.put('/:id',
  authMiddleware,
  requireEditor,
  updateArticleValidation,
  validateRequest,
  asyncHandler(articleController.updateArticle.bind(articleController))
);

/**
 * @swagger
 * /api/articles/{id}/publish:
 *   post:
 *     summary: 发布文章
 *     tags: [文章]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: id
 *         required: true
 *         schema:
 *           type: integer
 *         description: 文章ID
 *     responses:
 *       200:
 *         description: 发布成功
 *       401:
 *         description: 未授权
 *       403:
 *         description: 权限不足
 *       404:
 *         description: 文章不存在
 */
router.post('/:id/publish',
  authMiddleware,
  requireEditor,
  asyncHandler(articleController.publishArticle.bind(articleController))
);

/**
 * @swagger
 * /api/articles/{id}/unpublish:
 *   post:
 *     summary: 取消发布文章
 *     tags: [文章]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: id
 *         required: true
 *         schema:
 *           type: integer
 *         description: 文章ID
 *     responses:
 *       200:
 *         description: 取消发布成功
 *       401:
 *         description: 未授权
 *       403:
 *         description: 权限不足
 *       404:
 *         description: 文章不存在
 */
router.post('/:id/unpublish',
  authMiddleware,
  requireEditor,
  asyncHandler(articleController.unpublishArticle.bind(articleController))
);

/**
 * @swagger
 * /api/articles/{id}:
 *   delete:
 *     summary: 删除文章
 *     tags: [文章]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: id
 *         required: true
 *         schema:
 *           type: integer
 *         description: 文章ID
 *     responses:
 *       200:
 *         description: 删除成功
 *       401:
 *         description: 未授权
 *       403:
 *         description: 权限不足
 *       404:
 *         description: 文章不存在
 */
router.delete('/:id',
  authMiddleware,
  requireEditor,
  asyncHandler(articleController.deleteArticle.bind(articleController))
);

export default router;
