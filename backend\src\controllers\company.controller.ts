/**
 * 公司信息控制器
 */

import { Request, Response } from 'express';
import { DatabaseManager } from '../config/database';
import { logger } from '../utils/logger';
import { ApiResponse } from '../utils/response';
import { NotFoundError } from '../middleware/errorHandler';

/**
 * 公司信息控制器
 */
export class CompanyController {
  private prisma = DatabaseManager.getInstance().getPrisma();

  /**
   * 获取公司信息
   */
  public async getCompanyInfo(req: Request, res: Response): Promise<void> {
    try {
      // 获取第一条公司信息记录
      let companyInfo = await this.prisma.companyInfo.findFirst();

      // 如果没有记录，创建默认记录
      if (!companyInfo) {
        companyInfo = await this.prisma.companyInfo.create({
          data: {
            name: 'POS机产品展示网站',
            description: '专业的POS机产品展示平台，致力于为商户提供优质的支付解决方案。',
            address: '北京市朝阳区科技园区创新大厦A座1001室',
            phone: '************',
            email: '<EMAIL>',
            website: 'https://www.yourpos.com',
            founded: 2020,
            employees: '50-100人',
            revenue: '1000万-5000万',
            mission: '为商户提供安全、便捷、高效的支付解决方案',
            vision: '成为国内领先的支付设备服务商',
            values: '诚信、创新、专业、服务',
          },
        });
      }

      res.json(ApiResponse.success(companyInfo));
    } catch (error) {
      logger.error('获取公司信息失败', { error });
      throw error;
    }
  }

  /**
   * 更新公司信息（编辑者）
   */
  public async updateCompanyInfo(req: Request, res: Response): Promise<void> {
    const data = req.body;

    try {
      // 获取现有的公司信息
      let companyInfo = await this.prisma.companyInfo.findFirst();

      if (!companyInfo) {
        // 如果没有记录，创建新记录
        companyInfo = await this.prisma.companyInfo.create({
          data: {
            name: data.name || 'POS机产品展示网站',
            description: data.description,
            logo: data.logo,
            address: data.address,
            phone: data.phone,
            email: data.email,
            website: data.website,
            founded: data.founded,
            employees: data.employees,
            revenue: data.revenue,
            mission: data.mission,
            vision: data.vision,
            values: data.values,
          },
        });
      } else {
        // 更新现有记录
        companyInfo = await this.prisma.companyInfo.update({
          where: { id: companyInfo.id },
          data: {
            ...(data.name !== undefined && { name: data.name }),
            ...(data.description !== undefined && { description: data.description }),
            ...(data.logo !== undefined && { logo: data.logo }),
            ...(data.address !== undefined && { address: data.address }),
            ...(data.phone !== undefined && { phone: data.phone }),
            ...(data.email !== undefined && { email: data.email }),
            ...(data.website !== undefined && { website: data.website }),
            ...(data.founded !== undefined && { founded: data.founded }),
            ...(data.employees !== undefined && { employees: data.employees }),
            ...(data.revenue !== undefined && { revenue: data.revenue }),
            ...(data.mission !== undefined && { mission: data.mission }),
            ...(data.vision !== undefined && { vision: data.vision }),
            ...(data.values !== undefined && { values: data.values }),
          },
        });
      }

      logger.info('公司信息更新成功', {
        companyId: companyInfo.id,
        userId: req.user?.id,
        changes: Object.keys(data),
      });

      res.json(ApiResponse.updated(companyInfo));
    } catch (error) {
      logger.error('更新公司信息失败', { error, data });
      throw error;
    }
  }

  /**
   * 获取公司统计信息
   */
  public async getCompanyStats(req: Request, res: Response): Promise<void> {
    try {
      // 获取各种统计数据
      const [
        productCount,
        categoryCount,
        articleCount,
        inquiryCount,
        userCount,
      ] = await Promise.all([
        this.prisma.product.count({ where: { isActive: true } }),
        this.prisma.category.count({ where: { isActive: true } }),
        this.prisma.article.count({ where: { status: 'PUBLISHED' } }),
        this.prisma.contactInquiry.count(),
        this.prisma.user.count({ where: { isActive: true } }),
      ]);

      // 获取最近的咨询
      const recentInquiries = await this.prisma.contactInquiry.findMany({
        take: 5,
        orderBy: { createdAt: 'desc' },
        select: {
          id: true,
          name: true,
          subject: true,
          status: true,
          createdAt: true,
        },
      });

      // 获取热门产品
      const popularProducts = await this.prisma.product.findMany({
        where: { isActive: true },
        take: 5,
        orderBy: { viewCount: 'desc' },
        select: {
          id: true,
          name: true,
          viewCount: true,
          price: true,
        },
      });

      // 获取最新文章
      const recentArticles = await this.prisma.article.findMany({
        where: { status: 'PUBLISHED' },
        take: 5,
        orderBy: { publishedAt: 'desc' },
        select: {
          id: true,
          title: true,
          viewCount: true,
          publishedAt: true,
        },
      });

      const stats = {
        overview: {
          productCount,
          categoryCount,
          articleCount,
          inquiryCount,
          userCount,
        },
        recentInquiries,
        popularProducts,
        recentArticles,
      };

      res.json(ApiResponse.success(stats));
    } catch (error) {
      logger.error('获取公司统计信息失败', { error });
      throw error;
    }
  }

  /**
   * 获取公司发展历程
   */
  public async getCompanyHistory(req: Request, res: Response): Promise<void> {
    try {
      // 这里可以从数据库获取发展历程数据
      // 暂时返回静态数据，后续可以扩展为动态数据
      const history = [
        {
          year: 2020,
          title: '公司成立',
          description: '公司正式成立，专注于POS机产品展示和销售服务',
          image: '/images/history/2020.jpg',
        },
        {
          year: 2021,
          title: '产品线扩展',
          description: '引入多款智能POS机产品，服务商户超过1000家',
          image: '/images/history/2021.jpg',
        },
        {
          year: 2022,
          title: '技术升级',
          description: '推出云端管理平台，实现设备远程监控和管理',
          image: '/images/history/2022.jpg',
        },
        {
          year: 2023,
          title: '市场拓展',
          description: '业务覆盖全国主要城市，合作伙伴网络不断扩大',
          image: '/images/history/2023.jpg',
        },
        {
          year: 2024,
          title: '创新发展',
          description: '持续创新，为客户提供更优质的产品和服务',
          image: '/images/history/2024.jpg',
        },
      ];

      res.json(ApiResponse.success(history));
    } catch (error) {
      logger.error('获取公司发展历程失败', { error });
      throw error;
    }
  }

  /**
   * 获取团队信息
   */
  public async getTeamInfo(req: Request, res: Response): Promise<void> {
    try {
      // 这里可以从数据库获取团队信息
      // 暂时返回静态数据，后续可以扩展为动态数据
      const team = [
        {
          id: 1,
          name: '张总',
          position: '创始人兼CEO',
          avatar: '/images/team/ceo.jpg',
          description: '拥有15年支付行业经验，致力于为商户提供优质的支付解决方案',
          social: {
            linkedin: '#',
            weibo: '#',
          },
        },
        {
          id: 2,
          name: '李总',
          position: '技术总监',
          avatar: '/images/team/cto.jpg',
          description: '资深技术专家，负责产品技术架构和研发管理',
          social: {
            linkedin: '#',
            github: '#',
          },
        },
        {
          id: 3,
          name: '王总',
          position: '销售总监',
          avatar: '/images/team/sales.jpg',
          description: '丰富的销售管理经验，负责市场拓展和客户关系维护',
          social: {
            linkedin: '#',
            weibo: '#',
          },
        },
        {
          id: 4,
          name: '刘总',
          position: '运营总监',
          avatar: '/images/team/operations.jpg',
          description: '专业的运营管理专家，负责公司日常运营和流程优化',
          social: {
            linkedin: '#',
            weibo: '#',
          },
        },
      ];

      res.json(ApiResponse.success(team));
    } catch (error) {
      logger.error('获取团队信息失败', { error });
      throw error;
    }
  }

  /**
   * 获取公司资质证书
   */
  public async getCompanyCertificates(req: Request, res: Response): Promise<void> {
    try {
      // 这里可以从数据库获取资质证书信息
      // 暂时返回静态数据，后续可以扩展为动态数据
      const certificates = [
        {
          id: 1,
          name: '营业执照',
          image: '/images/certificates/business-license.jpg',
          description: '工商行政管理部门颁发的营业执照',
          issueDate: '2020-01-15',
          validUntil: '2030-01-14',
        },
        {
          id: 2,
          name: '银联认证',
          image: '/images/certificates/unionpay.jpg',
          description: '中国银联颁发的POS机产品认证证书',
          issueDate: '2020-06-20',
          validUntil: '2025-06-19',
        },
        {
          id: 3,
          name: 'ISO9001质量管理体系认证',
          image: '/images/certificates/iso9001.jpg',
          description: '国际标准化组织颁发的质量管理体系认证',
          issueDate: '2021-03-10',
          validUntil: '2024-03-09',
        },
        {
          id: 4,
          name: '高新技术企业证书',
          image: '/images/certificates/high-tech.jpg',
          description: '科技部门颁发的高新技术企业认定证书',
          issueDate: '2022-11-15',
          validUntil: '2025-11-14',
        },
      ];

      res.json(ApiResponse.success(certificates));
    } catch (error) {
      logger.error('获取公司资质证书失败', { error });
      throw error;
    }
  }
}
