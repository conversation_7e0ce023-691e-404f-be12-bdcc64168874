/**
 * 认证中间件
 */

import { Request, Response, NextFunction } from 'express';
import jwt from 'jsonwebtoken';
import { config } from '../config';
import { DatabaseManager } from '../config/database';
import { UnauthorizedError, ForbiddenError } from './errorHandler';
import { UserRole } from '@prisma/client';
import { logger } from '../utils/logger';

/**
 * 扩展Request接口以包含用户信息
 */
declare global {
  namespace Express {
    interface Request {
      user?: {
        id: number;
        email: string;
        name: string;
        role: UserRole;
        isActive: boolean;
      };
      requestId?: string;
    }
  }
}

/**
 * JWT载荷接口
 */
interface JWTPayload {
  userId: number;
  email: string;
  role: UserRole;
  iat: number;
  exp: number;
}

/**
 * 验证JWT令牌
 */
function verifyToken(token: string): JWTPayload {
  try {
    return jwt.verify(token, config.jwt.secret) as JWTPayload;
  } catch (error) {
    if (error instanceof jwt.TokenExpiredError) {
      throw new UnauthorizedError('访问令牌已过期');
    }
    if (error instanceof jwt.JsonWebTokenError) {
      throw new UnauthorizedError('无效的访问令牌');
    }
    throw new UnauthorizedError('令牌验证失败');
  }
}

/**
 * 从请求中提取令牌
 */
function extractToken(req: Request): string | null {
  // 从Authorization头中提取
  const authHeader = req.headers.authorization;
  if (authHeader && authHeader.startsWith('Bearer ')) {
    return authHeader.substring(7);
  }

  // 从Cookie中提取
  const cookieToken = req.cookies?.accessToken;
  if (cookieToken) {
    return cookieToken;
  }

  // 从查询参数中提取（不推荐，仅用于特殊情况）
  const queryToken = req.query.token as string;
  if (queryToken) {
    return queryToken;
  }

  return null;
}

/**
 * 认证中间件
 */
export async function authMiddleware(
  req: Request,
  res: Response,
  next: NextFunction
): Promise<void> {
  try {
    const token = extractToken(req);
    
    if (!token) {
      throw new UnauthorizedError('缺少访问令牌');
    }

    // 验证令牌
    const payload = verifyToken(token);

    // 从数据库获取用户信息
    const prisma = DatabaseManager.getInstance().getPrisma();
    const user = await prisma.user.findUnique({
      where: { id: payload.userId },
      select: {
        id: true,
        email: true,
        name: true,
        role: true,
        isActive: true,
      },
    });

    if (!user) {
      throw new UnauthorizedError('用户不存在');
    }

    if (!user.isActive) {
      throw new UnauthorizedError('用户账户已被禁用');
    }

    // 将用户信息添加到请求对象
    req.user = user;

    // 记录认证日志
    logger.info('用户认证成功', {
      userId: user.id,
      email: user.email,
      role: user.role,
      ip: req.ip,
      userAgent: req.get('User-Agent'),
      requestId: req.requestId,
    });

    next();
  } catch (error) {
    // 记录认证失败日志
    logger.warn('用户认证失败', {
      error: error instanceof Error ? error.message : '未知错误',
      ip: req.ip,
      userAgent: req.get('User-Agent'),
      requestId: req.requestId,
    });

    next(error);
  }
}

/**
 * 可选认证中间件（不强制要求认证）
 */
export async function optionalAuthMiddleware(
  req: Request,
  res: Response,
  next: NextFunction
): Promise<void> {
  try {
    const token = extractToken(req);
    
    if (!token) {
      return next();
    }

    // 验证令牌
    const payload = verifyToken(token);

    // 从数据库获取用户信息
    const prisma = DatabaseManager.getInstance().getPrisma();
    const user = await prisma.user.findUnique({
      where: { id: payload.userId },
      select: {
        id: true,
        email: true,
        name: true,
        role: true,
        isActive: true,
      },
    });

    if (user && user.isActive) {
      req.user = user;
    }

    next();
  } catch (error) {
    // 可选认证失败时不抛出错误，只是不设置用户信息
    next();
  }
}

/**
 * 角色权限检查中间件
 */
export function requireRole(...roles: UserRole[]) {
  return (req: Request, res: Response, next: NextFunction): void => {
    if (!req.user) {
      throw new UnauthorizedError('需要登录');
    }

    if (!roles.includes(req.user.role)) {
      throw new ForbiddenError('权限不足');
    }

    next();
  };
}

/**
 * 管理员权限检查中间件
 */
export function requireAdmin(req: Request, res: Response, next: NextFunction): void {
  if (!req.user) {
    throw new UnauthorizedError('需要登录');
  }

  if (req.user.role !== UserRole.ADMIN) {
    throw new ForbiddenError('需要管理员权限');
  }

  next();
}

/**
 * 编辑者权限检查中间件
 */
export function requireEditor(req: Request, res: Response, next: NextFunction): void {
  if (!req.user) {
    throw new UnauthorizedError('需要登录');
  }

  if (![UserRole.ADMIN, UserRole.EDITOR].includes(req.user.role)) {
    throw new ForbiddenError('需要编辑者权限');
  }

  next();
}

/**
 * 资源所有者检查中间件
 */
export function requireOwnerOrAdmin(userIdField: string = 'userId') {
  return async (req: Request, res: Response, next: NextFunction): Promise<void> => {
    if (!req.user) {
      throw new UnauthorizedError('需要登录');
    }

    // 管理员可以访问所有资源
    if (req.user.role === UserRole.ADMIN) {
      return next();
    }

    // 检查资源所有者
    const resourceUserId = req.params[userIdField] || req.body[userIdField];
    
    if (!resourceUserId) {
      throw new ForbiddenError('无法确定资源所有者');
    }

    if (parseInt(resourceUserId) !== req.user.id) {
      throw new ForbiddenError('只能访问自己的资源');
    }

    next();
  };
}

/**
 * API密钥认证中间件（用于第三方集成）
 */
export async function apiKeyMiddleware(
  req: Request,
  res: Response,
  next: NextFunction
): Promise<void> {
  try {
    const apiKey = req.headers['x-api-key'] as string;
    
    if (!apiKey) {
      throw new UnauthorizedError('缺少API密钥');
    }

    // 这里可以实现API密钥验证逻辑
    // 例如从数据库中查找有效的API密钥
    const prisma = DatabaseManager.getInstance().getPrisma();
    
    // 示例：假设API密钥存储在设置表中
    const setting = await prisma.setting.findUnique({
      where: { key: 'api_key' },
    });

    if (!setting || setting.value !== apiKey) {
      throw new UnauthorizedError('无效的API密钥');
    }

    // 记录API访问日志
    logger.info('API密钥认证成功', {
      apiKey: apiKey.substring(0, 8) + '...',
      ip: req.ip,
      userAgent: req.get('User-Agent'),
      requestId: req.requestId,
    });

    next();
  } catch (error) {
    logger.warn('API密钥认证失败', {
      error: error instanceof Error ? error.message : '未知错误',
      ip: req.ip,
      userAgent: req.get('User-Agent'),
      requestId: req.requestId,
    });

    next(error);
  }
}

/**
 * 生成JWT令牌
 */
export function generateToken(payload: {
  userId: number;
  email: string;
  role: UserRole;
}): string {
  return jwt.sign(payload, config.jwt.secret, {
    expiresIn: config.jwt.expiresIn,
  });
}

/**
 * 生成刷新令牌
 */
export function generateRefreshToken(payload: {
  userId: number;
  email: string;
  role: UserRole;
}): string {
  return jwt.sign(payload, config.jwt.secret, {
    expiresIn: config.jwt.refreshExpiresIn,
  });
}

/**
 * 验证刷新令牌
 */
export function verifyRefreshToken(token: string): JWTPayload {
  return verifyToken(token);
}
