---
description: 
globs: 
alwaysApply: true
---
# 项目结构规范

## 整体目录结构
```
pos-website/
├── frontend/                 # Nuxt.js 前端项目
│   ├── components/          # Vue组件
│   ├── pages/              # 页面路由
│   ├── layouts/            # 布局组件
│   ├── plugins/            # 插件
│   ├── middleware/         # 中间件
│   ├── composables/        # 组合式函数
│   ├── assets/             # 静态资源
│   ├── public/             # 公共文件
│   ├── server/             # 服务端API
│   ├── types/              # TypeScript类型定义
│   └── nuxt.config.ts      # Nuxt配置
├── backend/                 # Express后端API
│   ├── src/
│   │   ├── controllers/    # 控制器
│   │   ├── models/         # 数据模型
│   │   ├── routes/         # 路由定义
│   │   ├── middleware/     # 中间件
│   │   ├── services/       # 业务逻辑
│   │   ├── utils/          # 工具函数
│   │   ├── config/         # 配置文件
│   │   └── types/          # TypeScript类型
│   ├── prisma/             # Prisma数据库配置
│   ├── uploads/            # 文件上传目录
│   └── logs/               # 日志文件
├── admin/                   # Vue管理后台
│   ├── src/
│   │   ├── components/     # 组件
│   │   ├── views/          # 页面视图
│   │   ├── router/         # 路由配置
│   │   ├── store/          # Pinia状态管理
│   │   ├── api/            # API接口
│   │   ├── utils/          # 工具函数
│   │   ├── assets/         # 静态资源
│   │   └── types/          # TypeScript类型
│   └── public/             # 公共文件
├── installer/               # 一键安装向导
│   ├── web/                # 安装界面
│   ├── scripts/            # 安装脚本
│   └── templates/          # 配置模板
├── deployment/              # 部署相关
│   ├── nginx/              # Nginx配置
│   ├── pm2/                # PM2配置
│   ├── ssl/                # SSL证书
│   └── scripts/            # 部署脚本
├── docs/                    # 项目文档
│   ├── api/                # API文档
│   ├── deployment/         # 部署文档
│   └── development/        # 开发文档
└── shared/                  # 共享代码
    ├── types/              # 共享类型定义
    ├── constants/          # 常量定义
    └── utils/              # 共享工具函数
```

## 前端项目结构 (frontend/)

### 组件结构 (components/)
```
components/
├── common/                 # 通用组件
│   ├── Header.vue         # 网站头部
│   ├── Footer.vue         # 网站底部
│   ├── Navigation.vue     # 导航菜单
│   └── Loading.vue        # 加载组件
├── home/                  # 首页组件
│   ├── Hero.vue           # 英雄区域
│   ├── Features.vue       # 特性展示
│   └── Statistics.vue     # 统计数据
├── products/              # 产品相关组件
│   ├── ProductCard.vue    # 产品卡片
│   ├── ProductList.vue    # 产品列表
│   └── ProductDetail.vue  # 产品详情
├── company/               # 公司信息组件
│   ├── AboutUs.vue        # 关于我们
│   ├── Culture.vue        # 企业文化
│   └── Timeline.vue       # 发展历程
└── forms/                 # 表单组件
    ├── ContactForm.vue    # 联系表单
    └── SubscribeForm.vue  # 订阅表单
```

### 页面结构 (pages/)
```
pages/
├── index.vue              # 首页
├── about/                 # 关于我们
│   ├── index.vue         # 公司简介
│   ├── culture.vue       # 企业文化
│   └── history.vue       # 发展历程
├── products/              # 产品展示
│   ├── index.vue         # 产品列表
│   └── [slug].vue        # 产品详情
├── news/                  # 新闻资讯
│   ├── index.vue         # 新闻列表
│   └── [slug].vue        # 新闻详情
├── contact/               # 联系我们
│   └── index.vue
└── legal/                 # 法律条款
    ├── privacy.vue        # 隐私政策
    └── terms.vue          # 使用条款
```

## 后端项目结构 (backend/)

### 控制器结构 (controllers/)
```
controllers/
├── auth.controller.ts     # 认证控制器
├── company.controller.ts  # 公司信息控制器
├── product.controller.ts  # 产品管理控制器
├── news.controller.ts     # 新闻管理控制器
├── upload.controller.ts   # 文件上传控制器
└── analytics.controller.ts # 访问统计控制器
```

### 数据模型结构 (models/)
```
models/
├── User.ts               # 用户模型
├── Company.ts            # 公司信息模型
├── Product.ts            # 产品模型
├── News.ts               # 新闻模型
├── Category.ts           # 分类模型
└── Analytics.ts          # 访问统计模型
```

## 管理后台结构 (admin/)

### 视图结构 (views/)
```
views/
├── login/                # 登录页面
├── dashboard/            # 仪表板
├── company/              # 公司管理
│   ├── info.vue         # 基本信息
│   ├── culture.vue      # 企业文化
│   └── history.vue      # 发展历程
├── products/             # 产品管理
│   ├── list.vue         # 产品列表
│   ├── add.vue          # 添加产品
│   └── edit.vue         # 编辑产品
├── news/                 # 新闻管理
│   ├── list.vue         # 新闻列表
│   ├── add.vue          # 发布新闻
│   └── edit.vue         # 编辑新闻
├── seo/                  # SEO管理
│   ├── settings.vue     # SEO设置
│   └── sitemap.vue      # 站点地图
└── analytics/            # 数据分析
    ├── visitors.vue     # 访客统计
    └── reports.vue      # 报表分析
```

## 命名规范

### 文件命名
- **组件文件**: PascalCase (如: `ProductCard.vue`)
- **页面文件**: kebab-case (如: `product-detail.vue`)
- **工具文件**: camelCase (如: `formatDate.ts`)
- **配置文件**: kebab-case (如: `nuxt.config.ts`)

### 变量命名
- **变量/函数**: camelCase (如: `userName`, `getUserInfo()`)
- **常量**: UPPER_SNAKE_CASE (如: `API_BASE_URL`)
- **类名**: PascalCase (如: `UserService`)
- **接口**: PascalCase + I前缀 (如: `IUserData`)

### 目录命名
- 统一使用 kebab-case 或 camelCase
- 保持项目内一致性

