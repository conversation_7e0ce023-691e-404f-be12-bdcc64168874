/**
 * 系统设置路由
 */

import { Router } from 'express';
import { body } from 'express-validator';
import { SettingController } from '../controllers/setting.controller';
import { requireAdmin } from '../middleware/auth';
import { asyncHandler } from '../middleware/errorHandler';
import { validateRequest } from '../middleware/validation';

const router = Router();
const settingController = new SettingController();

/**
 * 设置更新验证规则
 */
const updateSettingValidation = [
  body('key')
    .trim()
    .isLength({ min: 1, max: 100 })
    .withMessage('设置键长度必须在1-100个字符之间'),
  body('value')
    .optional()
    .isLength({ max: 10000 })
    .withMessage('设置值不能超过10000个字符'),
  body('description')
    .optional()
    .isLength({ max: 500 })
    .withMessage('设置描述不能超过500个字符'),
];

/**
 * @swagger
 * /api/settings:
 *   get:
 *     summary: 获取所有设置
 *     tags: [系统设置]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: query
 *         name: category
 *         schema:
 *           type: string
 *         description: 设置分类
 *       - in: query
 *         name: public
 *         schema:
 *           type: boolean
 *         description: 是否只获取公开设置
 *     responses:
 *       200:
 *         description: 获取成功
 *       401:
 *         description: 未授权
 *       403:
 *         description: 权限不足
 */
router.get('/',
  requireAdmin,
  asyncHandler(settingController.getSettings.bind(settingController))
);

/**
 * @swagger
 * /api/settings/public:
 *   get:
 *     summary: 获取公开设置
 *     tags: [系统设置]
 *     responses:
 *       200:
 *         description: 获取成功
 */
router.get('/public',
  asyncHandler(settingController.getPublicSettings.bind(settingController))
);

/**
 * @swagger
 * /api/settings/{key}:
 *   get:
 *     summary: 获取单个设置
 *     tags: [系统设置]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: key
 *         required: true
 *         schema:
 *           type: string
 *         description: 设置键
 *     responses:
 *       200:
 *         description: 获取成功
 *       401:
 *         description: 未授权
 *       403:
 *         description: 权限不足
 *       404:
 *         description: 设置不存在
 */
router.get('/:key',
  requireAdmin,
  asyncHandler(settingController.getSetting.bind(settingController))
);

/**
 * @swagger
 * /api/settings:
 *   post:
 *     summary: 创建设置
 *     tags: [系统设置]
 *     security:
 *       - bearerAuth: []
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required:
 *               - key
 *               - value
 *             properties:
 *               key:
 *                 type: string
 *                 description: 设置键
 *               value:
 *                 type: string
 *                 description: 设置值
 *               description:
 *                 type: string
 *                 description: 设置描述
 *               category:
 *                 type: string
 *                 description: 设置分类
 *               isPublic:
 *                 type: boolean
 *                 description: 是否公开
 *     responses:
 *       201:
 *         description: 创建成功
 *       400:
 *         description: 请求参数错误
 *       401:
 *         description: 未授权
 *       403:
 *         description: 权限不足
 */
router.post('/',
  requireAdmin,
  updateSettingValidation,
  validateRequest,
  asyncHandler(settingController.createSetting.bind(settingController))
);

/**
 * @swagger
 * /api/settings/{key}:
 *   put:
 *     summary: 更新设置
 *     tags: [系统设置]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: key
 *         required: true
 *         schema:
 *           type: string
 *         description: 设置键
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             properties:
 *               value:
 *                 type: string
 *                 description: 设置值
 *               description:
 *                 type: string
 *                 description: 设置描述
 *               category:
 *                 type: string
 *                 description: 设置分类
 *               isPublic:
 *                 type: boolean
 *                 description: 是否公开
 *     responses:
 *       200:
 *         description: 更新成功
 *       400:
 *         description: 请求参数错误
 *       401:
 *         description: 未授权
 *       403:
 *         description: 权限不足
 *       404:
 *         description: 设置不存在
 */
router.put('/:key',
  requireAdmin,
  body('value')
    .optional()
    .isLength({ max: 10000 })
    .withMessage('设置值不能超过10000个字符'),
  body('description')
    .optional()
    .isLength({ max: 500 })
    .withMessage('设置描述不能超过500个字符'),
  validateRequest,
  asyncHandler(settingController.updateSetting.bind(settingController))
);

/**
 * @swagger
 * /api/settings/batch:
 *   put:
 *     summary: 批量更新设置
 *     tags: [系统设置]
 *     security:
 *       - bearerAuth: []
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             additionalProperties:
 *               type: string
 *             description: 设置键值对
 *     responses:
 *       200:
 *         description: 更新成功
 *       400:
 *         description: 请求参数错误
 *       401:
 *         description: 未授权
 *       403:
 *         description: 权限不足
 */
router.put('/batch',
  requireAdmin,
  asyncHandler(settingController.batchUpdateSettings.bind(settingController))
);

/**
 * @swagger
 * /api/settings/{key}:
 *   delete:
 *     summary: 删除设置
 *     tags: [系统设置]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: key
 *         required: true
 *         schema:
 *           type: string
 *         description: 设置键
 *     responses:
 *       200:
 *         description: 删除成功
 *       401:
 *         description: 未授权
 *       403:
 *         description: 权限不足
 *       404:
 *         description: 设置不存在
 */
router.delete('/:key',
  requireAdmin,
  asyncHandler(settingController.deleteSetting.bind(settingController))
);

export default router;
