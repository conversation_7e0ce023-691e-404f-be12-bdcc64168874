<template>
  <footer class="bg-gray-900 text-white">
    <!-- 主要内容区域 -->
    <div class="container py-12">
      <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8">
        <!-- 公司信息 -->
        <div class="space-y-4">
          <div class="flex items-center space-x-2">
            <NuxtImg
              src="/images/logo-white.png"
              alt="POS机产品展示网站"
              width="32"
              height="32"
              class="w-8 h-8"
            />
            <span class="text-lg font-bold">POS机展示</span>
          </div>
          <p class="text-gray-300 text-sm leading-relaxed">
            专业的POS机产品展示平台，致力于为商户提供优质的支付解决方案。
          </p>
          <div class="flex space-x-4">
            <a
              href="#"
              class="text-gray-400 hover:text-white transition-colors"
              aria-label="微信"
            >
              <Icon name="simple-icons:wechat" class="w-5 h-5" />
            </a>
            <a
              href="#"
              class="text-gray-400 hover:text-white transition-colors"
              aria-label="微博"
            >
              <Icon name="simple-icons:sinaweibo" class="w-5 h-5" />
            </a>
            <a
              href="#"
              class="text-gray-400 hover:text-white transition-colors"
              aria-label="QQ"
            >
              <Icon name="simple-icons:tencentqq" class="w-5 h-5" />
            </a>
          </div>
        </div>

        <!-- 产品分类 -->
        <div class="space-y-4">
          <h3 class="text-lg font-semibold">产品分类</h3>
          <ul class="space-y-2">
            <li v-for="category in productCategories" :key="category.slug">
              <NuxtLink
                :to="`/products/category/${category.slug}`"
                class="text-gray-300 hover:text-white transition-colors text-sm"
              >
                {{ category.name }}
              </NuxtLink>
            </li>
          </ul>
        </div>

        <!-- 快速链接 -->
        <div class="space-y-4">
          <h3 class="text-lg font-semibold">快速链接</h3>
          <ul class="space-y-2">
            <li v-for="link in quickLinks" :key="link.href">
              <NuxtLink
                :to="link.href"
                class="text-gray-300 hover:text-white transition-colors text-sm"
              >
                {{ link.name }}
              </NuxtLink>
            </li>
          </ul>
        </div>

        <!-- 联系信息 -->
        <div class="space-y-4">
          <h3 class="text-lg font-semibold">联系我们</h3>
          <div class="space-y-3">
            <div class="flex items-start space-x-3">
              <Icon name="heroicons:map-pin" class="w-5 h-5 text-gray-400 mt-0.5 flex-shrink-0" />
              <span class="text-gray-300 text-sm">
                北京市朝阳区科技园区创新大厦A座1001室
              </span>
            </div>
            <div class="flex items-center space-x-3">
              <Icon name="heroicons:phone" class="w-5 h-5 text-gray-400 flex-shrink-0" />
              <span class="text-gray-300 text-sm">************</span>
            </div>
            <div class="flex items-center space-x-3">
              <Icon name="heroicons:envelope" class="w-5 h-5 text-gray-400 flex-shrink-0" />
              <span class="text-gray-300 text-sm"><EMAIL></span>
            </div>
            <div class="flex items-center space-x-3">
              <Icon name="heroicons:clock" class="w-5 h-5 text-gray-400 flex-shrink-0" />
              <span class="text-gray-300 text-sm">周一至周五 9:00-18:00</span>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 分隔线 -->
    <div class="border-t border-gray-800"></div>

    <!-- 底部版权信息 -->
    <div class="container py-6">
      <div class="flex flex-col md:flex-row items-center justify-between space-y-4 md:space-y-0">
        <div class="text-gray-400 text-sm">
          © {{ currentYear }} POS机产品展示网站. 保留所有权利.
        </div>
        <div class="flex items-center space-x-6 text-sm">
          <NuxtLink
            to="/legal/privacy"
            class="text-gray-400 hover:text-white transition-colors"
          >
            隐私政策
          </NuxtLink>
          <NuxtLink
            to="/legal/terms"
            class="text-gray-400 hover:text-white transition-colors"
          >
            使用条款
          </NuxtLink>
          <NuxtLink
            to="/sitemap"
            class="text-gray-400 hover:text-white transition-colors"
          >
            网站地图
          </NuxtLink>
        </div>
      </div>
    </div>

    <!-- 备案信息 -->
    <div class="bg-gray-950 py-3">
      <div class="container text-center">
        <p class="text-gray-500 text-xs">
          京ICP备12345678号-1 | 京公网安备11010502012345号
        </p>
      </div>
    </div>
  </footer>
</template>

<script setup lang="ts">
// 当前年份
const currentYear = new Date().getFullYear()

// 产品分类数据
const productCategories = ref([
  { name: '移动POS机', slug: 'mobile-pos' },
  { name: '智能POS机', slug: 'smart-pos' },
  { name: '传统POS机', slug: 'traditional-pos' },
  { name: '无线POS机', slug: 'wireless-pos' }
])

// 快速链接数据
const quickLinks = [
  { name: '关于我们', href: '/about' },
  { name: '企业文化', href: '/about/culture' },
  { name: '发展历程', href: '/about/history' },
  { name: '新闻资讯', href: '/news' },
  { name: '技术支持', href: '/support' },
  { name: '招聘信息', href: '/careers' }
]

// 获取产品分类数据
onMounted(async () => {
  try {
    const { data } = await $fetch('/api/categories?limit=6')
    if (data && data.length > 0) {
      productCategories.value = data.map((category: any) => ({
        name: category.name,
        slug: category.slug
      }))
    }
  } catch (error) {
    console.warn('Failed to load categories for footer:', error)
  }
})
</script>
