/**
 * 客户端初始化插件
 */

import Toast from 'vue-toastification'
import 'vue-toastification/dist/index.css'

export default defineNuxtPlugin(async (nuxtApp) => {
  // 注册Toast通知
  nuxtApp.vueApp.use(Toast, {
    position: 'top-right',
    timeout: 5000,
    closeOnClick: true,
    pauseOnFocusLoss: true,
    pauseOnHover: true,
    draggable: true,
    draggablePercent: 0.6,
    showCloseButtonOnHover: false,
    hideProgressBar: false,
    closeButton: 'button',
    icon: true,
    rtl: false
  })

  // 初始化认证状态
  const authStore = useAuthStore()
  await authStore.initAuth()

  // 页面访问统计
  const analytics = {
    trackPageView: (path: string) => {
      // Google Analytics
      if (typeof gtag !== 'undefined') {
        gtag('config', 'GA_TRACKING_ID', {
          page_path: path
        })
      }
      
      // 百度统计
      if (typeof _hmt !== 'undefined') {
        _hmt.push(['_trackPageview', path])
      }
      
      // 自定义统计
      if (process.client) {
        $fetch('/api/analytics/track', {
          method: 'POST',
          body: {
            path,
            title: document.title,
            referrer: document.referrer,
            userAgent: navigator.userAgent
          }
        }).catch(() => {
          // 忽略统计错误
        })
      }
    },
    
    trackEvent: (action: string, category: string, label?: string, value?: number) => {
      // Google Analytics
      if (typeof gtag !== 'undefined') {
        gtag('event', action, {
          event_category: category,
          event_label: label,
          value: value
        })
      }
      
      // 百度统计
      if (typeof _hmt !== 'undefined') {
        _hmt.push(['_trackEvent', category, action, label, value])
      }
    }
  }

  // 提供给全局使用
  nuxtApp.provide('analytics', analytics)

  // 全局错误处理
  nuxtApp.vueApp.config.errorHandler = (error, instance, info) => {
    console.error('Vue error:', error, info)
    
    // 发送错误到监控服务
    if (process.client) {
      $fetch('/api/errors/report', {
        method: 'POST',
        body: {
          error: error.toString(),
          stack: error.stack,
          info,
          url: window.location.href,
          userAgent: navigator.userAgent
        }
      }).catch(() => {
        // 忽略错误报告失败
      })
    }
  }

  // 监听网络状态
  if (process.client) {
    const updateOnlineStatus = () => {
      const isOnline = navigator.onLine
      if (!isOnline) {
        nuxtApp.$toast.warning('网络连接已断开')
      } else {
        nuxtApp.$toast.success('网络连接已恢复')
      }
    }

    window.addEventListener('online', updateOnlineStatus)
    window.addEventListener('offline', updateOnlineStatus)
  }
})
