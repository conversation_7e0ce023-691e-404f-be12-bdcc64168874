// This is your Prisma schema file,
// learn more about it in the docs: https://pris.ly/d/prisma-schema

generator client {
  provider = "prisma-client-js"
}

datasource db {
  provider = "mysql"
  url      = env("DATABASE_URL")
}

// 用户表
model User {
  id          Int      @id @default(autoincrement())
  email       String   @unique @db.VarChar(255)
  password    String   @db.VarChar(255)
  name        String   @db.VarChar(100)
  avatar      String?  @db.VarChar(500)
  role        UserRole @default(USER)
  isActive    Boolean  @default(true) @map("is_active")
  lastLoginAt DateTime? @map("last_login_at")
  createdAt   DateTime @default(now()) @map("created_at")
  updatedAt   DateTime @updatedAt @map("updated_at")

  // 关联关系
  sessions    UserSession[]
  reviews     ProductReview[]
  inquiries   ContactInquiry[]
  articles    Article[]
  assignedInquiries ContactInquiry[] @relation("AssignedInquiries")
  pageViews   PageView[]

  @@map("users")
}

// 用户会话表
model UserSession {
  id           String   @id @default(cuid())
  userId       Int      @map("user_id")
  refreshToken String   @unique @db.VarChar(500) @map("refresh_token")
  userAgent    String?  @db.Text @map("user_agent")
  ipAddress    String?  @db.VarChar(45) @map("ip_address")
  expiresAt    DateTime @map("expires_at")
  createdAt    DateTime @default(now()) @map("created_at")

  // 关联关系
  user User @relation(fields: [userId], references: [id], onDelete: Cascade)

  @@map("user_sessions")
}

// 分类表
model Category {
  id          Int      @id @default(autoincrement())
  name        String   @db.VarChar(100)
  slug        String   @unique @db.VarChar(100)
  description String?  @db.Text
  image       String?  @db.VarChar(500)
  parentId    Int?     @map("parent_id")
  sortOrder   Int      @default(0) @map("sort_order")
  isActive    Boolean  @default(true) @map("is_active")
  createdAt   DateTime @default(now()) @map("created_at")
  updatedAt   DateTime @updatedAt @map("updated_at")

  // 关联关系
  parent   Category?  @relation("CategoryHierarchy", fields: [parentId], references: [id])
  children Category[] @relation("CategoryHierarchy")
  products Product[]

  @@map("categories")
}

// 产品表
model Product {
  id          Int      @id @default(autoincrement())
  name        String   @db.VarChar(255)
  slug        String   @unique @db.VarChar(255)
  description String?  @db.Text
  shortDesc   String?  @db.VarChar(500) @map("short_desc")
  sku         String?  @unique @db.VarChar(100)
  price       Decimal  @db.Decimal(10, 2)
  originalPrice Decimal? @db.Decimal(10, 2) @map("original_price")
  categoryId  Int      @map("category_id")
  brand       String?  @db.VarChar(100)
  model       String?  @db.VarChar(100)
  weight      Float?
  dimensions  String?  @db.VarChar(100)
  color       String?  @db.VarChar(50)
  material    String?  @db.VarChar(100)
  warranty    String?  @db.VarChar(200)
  stock       Int      @default(0)
  minStock    Int      @default(0) @map("min_stock")
  maxStock    Int      @default(999999) @map("max_stock")
  isActive    Boolean  @default(true) @map("is_active")
  isFeatured  Boolean  @default(false) @map("is_featured")
  isNew       Boolean  @default(false) @map("is_new")
  viewCount   Int      @default(0) @map("view_count")
  seoTitle    String?  @db.VarChar(255) @map("seo_title")
  seoDesc     String?  @db.VarChar(500) @map("seo_desc")
  seoKeywords String?  @db.VarChar(500) @map("seo_keywords")
  createdAt   DateTime @default(now()) @map("created_at")
  updatedAt   DateTime @updatedAt @map("updated_at")

  // 关联关系
  category      Category           @relation(fields: [categoryId], references: [id])
  images        ProductImage[]
  specifications ProductSpecification[]
  reviews       ProductReview[]
  tags          ProductTag[]

  @@map("products")
}

// 产品图片表
model ProductImage {
  id        Int     @id @default(autoincrement())
  productId Int     @map("product_id")
  url       String  @db.VarChar(500)
  alt       String? @db.VarChar(255)
  title     String? @db.VarChar(255)
  sortOrder Int     @default(0) @map("sort_order")
  isPrimary Boolean @default(false) @map("is_primary")
  createdAt DateTime @default(now()) @map("created_at")

  // 关联关系
  product Product @relation(fields: [productId], references: [id], onDelete: Cascade)

  @@map("product_images")
}

// 产品规格表
model ProductSpecification {
  id        Int    @id @default(autoincrement())
  productId Int    @map("product_id")
  name      String @db.VarChar(100)
  value     String @db.VarChar(500)
  unit      String? @db.VarChar(20)
  sortOrder Int    @default(0) @map("sort_order")

  // 关联关系
  product Product @relation(fields: [productId], references: [id], onDelete: Cascade)

  @@map("product_specifications")
}

// 产品评价表
model ProductReview {
  id        Int      @id @default(autoincrement())
  productId Int      @map("product_id")
  userId    Int?     @map("user_id")
  name      String   @db.VarChar(100)
  email     String   @db.VarChar(255)
  rating    Int      @db.TinyInt
  title     String?  @db.VarChar(255)
  content   String   @db.Text
  isApproved Boolean @default(false) @map("is_approved")
  createdAt DateTime @default(now()) @map("created_at")
  updatedAt DateTime @updatedAt @map("updated_at")

  // 关联关系
  product Product @relation(fields: [productId], references: [id], onDelete: Cascade)
  user    User?   @relation(fields: [userId], references: [id], onDelete: SetNull)

  @@map("product_reviews")
}

// 产品标签表
model Tag {
  id        Int      @id @default(autoincrement())
  name      String   @unique @db.VarChar(50)
  slug      String   @unique @db.VarChar(50)
  color     String?  @db.VarChar(7)
  createdAt DateTime @default(now()) @map("created_at")

  // 关联关系
  products ProductTag[]
  articles ArticleTag[]

  @@map("tags")
}

// 产品标签关联表
model ProductTag {
  productId Int @map("product_id")
  tagId     Int @map("tag_id")

  // 关联关系
  product Product @relation(fields: [productId], references: [id], onDelete: Cascade)
  tag     Tag     @relation(fields: [tagId], references: [id], onDelete: Cascade)

  @@id([productId, tagId])
  @@map("product_tags")
}

// 公司信息表
model CompanyInfo {
  id          Int      @id @default(autoincrement())
  name        String   @db.VarChar(255)
  description String?  @db.Text
  logo        String?  @db.VarChar(500)
  address     String?  @db.VarChar(500)
  phone       String?  @db.VarChar(50)
  email       String?  @db.VarChar(255)
  website     String?  @db.VarChar(255)
  founded     Int?
  employees   String?  @db.VarChar(50)
  revenue     String?  @db.VarChar(50)
  mission     String?  @db.Text
  vision      String?  @db.Text
  values      String?  @db.Text
  createdAt   DateTime @default(now()) @map("created_at")
  updatedAt   DateTime @updatedAt @map("updated_at")

  @@map("company_info")
}

// 新闻文章表
model Article {
  id          Int           @id @default(autoincrement())
  title       String        @db.VarChar(255)
  slug        String        @unique @db.VarChar(255)
  excerpt     String?       @db.VarChar(500)
  content     String        @db.LongText
  featuredImage String?     @db.VarChar(500) @map("featured_image")
  authorId    Int?          @map("author_id")
  categoryId  Int?          @map("category_id")
  status      ArticleStatus @default(DRAFT)
  isSticky    Boolean       @default(false) @map("is_sticky")
  viewCount   Int           @default(0) @map("view_count")
  seoTitle    String?       @db.VarChar(255) @map("seo_title")
  seoDesc     String?       @db.VarChar(500) @map("seo_desc")
  seoKeywords String?       @db.VarChar(500) @map("seo_keywords")
  publishedAt DateTime?     @map("published_at")
  createdAt   DateTime      @default(now()) @map("created_at")
  updatedAt   DateTime      @updatedAt @map("updated_at")

  // 关联关系
  author   User?            @relation(fields: [authorId], references: [id], onDelete: SetNull)
  category ArticleCategory? @relation(fields: [categoryId], references: [id], onDelete: SetNull)
  tags     ArticleTag[]

  @@map("articles")
}

// 文章分类表
model ArticleCategory {
  id          Int      @id @default(autoincrement())
  name        String   @db.VarChar(100)
  slug        String   @unique @db.VarChar(100)
  description String?  @db.Text
  sortOrder   Int      @default(0) @map("sort_order")
  createdAt   DateTime @default(now()) @map("created_at")
  updatedAt   DateTime @updatedAt @map("updated_at")

  // 关联关系
  articles Article[]

  @@map("article_categories")
}

// 文章标签关联表
model ArticleTag {
  articleId Int @map("article_id")
  tagId     Int @map("tag_id")

  // 关联关系
  article Article @relation(fields: [articleId], references: [id], onDelete: Cascade)
  tag     Tag     @relation(fields: [tagId], references: [id], onDelete: Cascade)

  @@id([articleId, tagId])
  @@map("article_tags")
}

// 联系咨询表
model ContactInquiry {
  id        Int           @id @default(autoincrement())
  name      String        @db.VarChar(100)
  email     String        @db.VarChar(255)
  phone     String?       @db.VarChar(50)
  company   String?       @db.VarChar(255)
  subject   String        @db.VarChar(255)
  message   String        @db.Text
  status    InquiryStatus @default(PENDING)
  userId    Int?          @map("user_id")
  assignedTo Int?         @map("assigned_to")
  repliedAt DateTime?     @map("replied_at")
  createdAt DateTime      @default(now()) @map("created_at")
  updatedAt DateTime      @updatedAt @map("updated_at")

  // 关联关系
  user     User? @relation(fields: [userId], references: [id], onDelete: SetNull)
  assignee User? @relation("AssignedInquiries", fields: [assignedTo], references: [id], onDelete: SetNull)

  @@map("contact_inquiries")
}

// 网站设置表
model Setting {
  id          Int      @id @default(autoincrement())
  key         String   @unique @db.VarChar(100)
  value       String?  @db.Text
  description String?  @db.Text
  category    String?  @db.VarChar(50)
  isPublic    Boolean  @default(false) @map("is_public")
  createdAt   DateTime @default(now()) @map("created_at")
  updatedAt   DateTime @updatedAt @map("updated_at")

  @@map("settings")
}

// 页面访问记录表
model PageView {
  id            Int      @id @default(autoincrement())
  path          String   @db.VarChar(500)
  title         String?  @db.VarChar(255)
  referrer      String?  @db.VarChar(500)
  ipAddress     String?  @db.VarChar(45) @map("ip_address")
  userAgent     String?  @db.Text @map("user_agent")
  deviceType    String?  @db.VarChar(50) @map("device_type")
  deviceVendor  String?  @db.VarChar(100) @map("device_vendor")
  deviceModel   String?  @db.VarChar(100) @map("device_model")
  browserName   String?  @db.VarChar(100) @map("browser_name")
  browserVersion String? @db.VarChar(50) @map("browser_version")
  osName        String?  @db.VarChar(100) @map("os_name")
  osVersion     String?  @db.VarChar(50) @map("os_version")
  userId        Int?     @map("user_id")
  createdAt     DateTime @default(now()) @map("created_at")

  // 关联关系
  user User? @relation(fields: [userId], references: [id], onDelete: SetNull)

  @@map("page_views")
}

// 访问统计表
model Analytics {
  id         Int      @id @default(autoincrement())
  date       DateTime @db.Date
  pagePath   String   @db.VarChar(500) @map("page_path")
  pageTitle  String?  @db.VarChar(255) @map("page_title")
  visitors   Int      @default(0)
  pageViews  Int      @default(0) @map("page_views")
  bounceRate Float?   @db.Float @map("bounce_rate")
  avgDuration Float?  @db.Float @map("avg_duration")
  referrer   String?  @db.VarChar(500)
  userAgent  String?  @db.Text @map("user_agent")
  ipAddress  String?  @db.VarChar(45) @map("ip_address")
  country    String?  @db.VarChar(100)
  city       String?  @db.VarChar(100)
  device     String?  @db.VarChar(50)
  browser    String?  @db.VarChar(50)
  os         String?  @db.VarChar(50)
  createdAt  DateTime @default(now()) @map("created_at")

  @@unique([date, pagePath])
  @@map("analytics")
}

// 系统日志表
model SystemLog {
  id        Int       @id @default(autoincrement())
  level     LogLevel
  message   String    @db.Text
  context   Json?
  userId    Int?      @map("user_id")
  ipAddress String?   @db.VarChar(45) @map("ip_address")
  userAgent String?   @db.Text @map("user_agent")
  createdAt DateTime  @default(now()) @map("created_at")

  @@map("system_logs")
}

// 枚举定义
enum UserRole {
  ADMIN
  EDITOR
  USER
}

enum ArticleStatus {
  DRAFT
  PUBLISHED
  ARCHIVED
}

enum InquiryStatus {
  PENDING
  IN_PROGRESS
  RESOLVED
  CLOSED
}

enum LogLevel {
  ERROR
  WARN
  INFO
  DEBUG
}
