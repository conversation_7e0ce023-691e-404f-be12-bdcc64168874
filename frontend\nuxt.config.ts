// https://nuxt.com/docs/api/configuration/nuxt-config
export default defineNuxtConfig({
  devtools: { enabled: true },
  
  // CSS框架
  css: ['~/assets/css/main.css'],
  
  // 模块配置
  modules: [
    '@nuxt/ui',
    '@nuxt/image',
    '@nuxtjs/seo',
    '@pinia/nuxt',
    '@vueuse/nuxt',
    '@nuxtjs/sitemap',
    '@nuxtjs/robots'
  ],

  // 运行时配置
  runtimeConfig: {
    // 私有配置（仅服务端可用）
    jwtSecret: process.env.JWT_SECRET,
    
    // 公共配置（客户端和服务端都可用）
    public: {
      apiBase: process.env.NUXT_PUBLIC_API_BASE || 'http://localhost:3001/api',
      siteUrl: process.env.NUXT_PUBLIC_SITE_URL || 'http://localhost:3000',
      siteName: process.env.NUXT_PUBLIC_SITE_NAME || 'POS机产品展示网站',
      siteDescription: process.env.NUXT_PUBLIC_SITE_DESCRIPTION || '专业的POS机产品展示平台，提供最新的支付终端设备信息',
      googleAnalyticsId: process.env.NUXT_PUBLIC_GA_ID,
      baiduAnalyticsId: process.env.NUXT_PUBLIC_BAIDU_ID
    }
  },

  // SEO配置
  site: {
    url: process.env.NUXT_PUBLIC_SITE_URL || 'http://localhost:3000',
    name: 'POS机产品展示网站',
    description: '专业的POS机产品展示平台，提供最新的支付终端设备信息',
    defaultLocale: 'zh-CN'
  },

  // SEO模块配置
  seo: {
    redirectToCanonicalSiteUrl: true,
    fallbackTitle: 'POS机产品展示网站',
    templateParams: {
      separator: ' | ',
      siteName: 'POS机产品展示网站'
    }
  },

  // 站点地图配置
  sitemap: {
    hostname: process.env.NUXT_PUBLIC_SITE_URL || 'http://localhost:3000',
    gzip: true,
    routes: async () => {
      // 这里可以动态生成路由
      return []
    }
  },

  // robots.txt配置
  robots: {
    UserAgent: '*',
    Allow: '/',
    Disallow: ['/admin', '/api'],
    Sitemap: (process.env.NUXT_PUBLIC_SITE_URL || 'http://localhost:3000') + '/sitemap.xml'
  },

  // 图片优化配置
  image: {
    quality: 80,
    format: ['webp', 'jpg'],
    screens: {
      xs: 320,
      sm: 640,
      md: 768,
      lg: 1024,
      xl: 1280,
      xxl: 1536
    },
    presets: {
      avatar: {
        modifiers: {
          format: 'webp',
          width: 50,
          height: 50
        }
      },
      product: {
        modifiers: {
          format: 'webp',
          width: 400,
          height: 300,
          quality: 85
        }
      },
      hero: {
        modifiers: {
          format: 'webp',
          width: 1200,
          height: 600,
          quality: 90
        }
      }
    }
  },

  // Nitro配置
  nitro: {
    prerender: {
      routes: [
        '/',
        '/about',
        '/products',
        '/contact',
        '/sitemap.xml'
      ]
    },
    // 压缩配置
    compressPublicAssets: true
  },

  // 构建配置
  build: {
    transpile: ['vue-toastification']
  },

  // TypeScript配置
  typescript: {
    strict: true,
    typeCheck: true
  },

  // 实验性功能
  experimental: {
    payloadExtraction: false
  },

  // 应用配置
  app: {
    head: {
      charset: 'utf-8',
      viewport: 'width=device-width, initial-scale=1',
      title: 'POS机产品展示网站',
      meta: [
        { name: 'description', content: '专业的POS机产品展示平台，提供最新的支付终端设备信息' },
        { name: 'keywords', content: 'POS机,支付终端,移动支付,智能POS,收银设备' },
        { name: 'author', content: 'POS机产品展示网站' },
        { name: 'robots', content: 'index,follow' },
        { property: 'og:type', content: 'website' },
        { property: 'og:site_name', content: 'POS机产品展示网站' },
        { name: 'twitter:card', content: 'summary_large_image' }
      ],
      link: [
        { rel: 'icon', type: 'image/x-icon', href: '/favicon.ico' },
        { rel: 'canonical', href: process.env.NUXT_PUBLIC_SITE_URL || 'http://localhost:3000' }
      ]
    }
  },

  // 服务端渲染配置
  ssr: true,

  // 路由配置
  router: {
    options: {
      scrollBehaviorType: 'smooth'
    }
  }
})
