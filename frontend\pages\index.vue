<template>
  <div>
    <!-- 英雄区域 -->
    <HomeHero />
    
    <!-- 特色产品 -->
    <HomeFeaturedProducts />
    
    <!-- 公司优势 -->
    <HomeAdvantages />
    
    <!-- 统计数据 -->
    <HomeStatistics />
    
    <!-- 新闻资讯 -->
    <HomeNews />
    
    <!-- 客户评价 -->
    <HomeTestimonials />
    
    <!-- 联系我们 -->
    <HomeContact />
  </div>
</template>

<script setup lang="ts">
// 页面SEO配置
useSeoMeta({
  title: 'POS机产品展示 - 专业支付终端设备',
  description: '提供最新最全的POS机产品信息，包括移动POS、智能POS、传统POS等各类支付终端设备',
  keywords: 'POS机,支付终端,移动支付,智能POS,收银设备',
  author: '公司名称',
  robots: 'index,follow',
  
  // Open Graph
  ogTitle: 'POS机产品展示 - 专业支付终端设备',
  ogDescription: '提供最新最全的POS机产品信息，包括移动POS、智能POS、传统POS等各类支付终端设备',
  ogImage: '/images/og-image.jpg',
  ogUrl: 'https://www.yourpos.com',
  ogType: 'website',
  ogSiteName: 'POS机产品展示网站',
  
  // Twitter Card
  twitterCard: 'summary_large_image',
  twitterSite: '@yourpos',
  twitterCreator: '@yourpos',
  twitterTitle: 'POS机产品展示 - 专业支付终端设备',
  twitterDescription: '提供最新最全的POS机产品信息',
  twitterImage: '/images/twitter-image.jpg'
})

// 结构化数据
useSchemaOrg([
  defineOrganization({
    name: '公司名称',
    url: 'https://www.yourpos.com',
    logo: 'https://www.yourpos.com/logo.png',
    sameAs: [
      'https://www.facebook.com/yourpos',
      'https://www.twitter.com/yourpos',
      'https://www.linkedin.com/company/yourpos'
    ]
  }),
  defineWebSite({
    name: 'POS机产品展示网站',
    url: 'https://www.yourpos.com',
    potentialAction: {
      '@type': 'SearchAction',
      target: 'https://www.yourpos.com/search?q={search_term_string}',
      'query-input': 'required name=search_term_string'
    }
  })
])

// 页面数据获取
const { data: pageData } = await useLazyAsyncData('home-page', async () => {
  const [featuredProducts, companyInfo, latestNews] = await Promise.all([
    $fetch('/api/products/featured'),
    $fetch('/api/company/info'),
    $fetch('/api/articles?limit=6&status=published')
  ])
  
  return {
    featuredProducts,
    companyInfo,
    latestNews
  }
})

// 提供数据给子组件
provide('pageData', pageData)
</script>
