/**
 * 分类控制器
 */

import { Request, Response } from 'express';
import { DatabaseManager } from '../config/database';
import { logger } from '../utils/logger';
import { ApiResponse } from '../utils/response';
import { NotFoundError, ValidationError } from '../middleware/errorHandler';
import type { ICreateCategoryDto } from '@/shared/types';

/**
 * 分类控制器
 */
export class CategoryController {
  private prisma = DatabaseManager.getInstance().getPrisma();

  /**
   * 获取分类列表
   */
  public async getCategories(req: Request, res: Response): Promise<void> {
    const { includeProducts = false } = req.query;

    try {
      const categories = await this.prisma.category.findMany({
        where: { isActive: true },
        include: {
          parent: {
            select: {
              id: true,
              name: true,
              slug: true,
            },
          },
          children: {
            where: { isActive: true },
            select: {
              id: true,
              name: true,
              slug: true,
              description: true,
              image: true,
              sortOrder: true,
            },
            orderBy: { sortOrder: 'asc' },
          },
          ...(includeProducts === 'true' && {
            products: {
              where: { isActive: true },
              select: {
                id: true,
                name: true,
                slug: true,
                price: true,
                images: {
                  where: { isPrimary: true },
                  take: 1,
                  select: {
                    url: true,
                    alt: true,
                  },
                },
              },
              take: 5,
              orderBy: { viewCount: 'desc' },
            },
          }),
          _count: {
            select: {
              products: {
                where: { isActive: true },
              },
            },
          },
        },
        orderBy: { sortOrder: 'asc' },
      });

      res.json(ApiResponse.success(categories));
    } catch (error) {
      logger.error('获取分类列表失败', { error });
      throw error;
    }
  }

  /**
   * 获取分类树结构
   */
  public async getCategoryTree(req: Request, res: Response): Promise<void> {
    try {
      // 获取所有顶级分类（没有父分类的）
      const rootCategories = await this.prisma.category.findMany({
        where: {
          parentId: null,
          isActive: true,
        },
        include: {
          children: {
            where: { isActive: true },
            include: {
              children: {
                where: { isActive: true },
                select: {
                  id: true,
                  name: true,
                  slug: true,
                  description: true,
                  image: true,
                  sortOrder: true,
                  _count: {
                    select: {
                      products: {
                        where: { isActive: true },
                      },
                    },
                  },
                },
                orderBy: { sortOrder: 'asc' },
              },
              _count: {
                select: {
                  products: {
                    where: { isActive: true },
                  },
                },
              },
            },
            orderBy: { sortOrder: 'asc' },
          },
          _count: {
            select: {
              products: {
                where: { isActive: true },
              },
            },
          },
        },
        orderBy: { sortOrder: 'asc' },
      });

      res.json(ApiResponse.success(rootCategories));
    } catch (error) {
      logger.error('获取分类树失败', { error });
      throw error;
    }
  }

  /**
   * 获取分类详情
   */
  public async getCategory(req: Request, res: Response): Promise<void> {
    const { slug } = req.params;

    try {
      const category = await this.prisma.category.findUnique({
        where: { slug },
        include: {
          parent: {
            select: {
              id: true,
              name: true,
              slug: true,
            },
          },
          children: {
            where: { isActive: true },
            select: {
              id: true,
              name: true,
              slug: true,
              description: true,
              image: true,
              _count: {
                select: {
                  products: {
                    where: { isActive: true },
                  },
                },
              },
            },
            orderBy: { sortOrder: 'asc' },
          },
          products: {
            where: { isActive: true },
            include: {
              images: {
                where: { isPrimary: true },
                take: 1,
                select: {
                  url: true,
                  alt: true,
                },
              },
              tags: {
                include: {
                  tag: {
                    select: {
                      id: true,
                      name: true,
                      slug: true,
                      color: true,
                    },
                  },
                },
              },
            },
            orderBy: [
              { isFeatured: 'desc' },
              { viewCount: 'desc' },
              { createdAt: 'desc' },
            ],
            take: 20,
          },
          _count: {
            select: {
              products: {
                where: { isActive: true },
              },
            },
          },
        },
      });

      if (!category) {
        throw new NotFoundError('分类');
      }

      res.json(ApiResponse.success(category));
    } catch (error) {
      logger.error('获取分类详情失败', { error, slug });
      throw error;
    }
  }

  /**
   * 创建分类（管理员）
   */
  public async createCategory(req: Request, res: Response): Promise<void> {
    const data: ICreateCategoryDto = req.body;

    try {
      // 检查slug是否已存在
      const existingCategory = await this.prisma.category.findUnique({
        where: { slug: data.slug },
      });

      if (existingCategory) {
        throw new ValidationError('分类标识符已存在');
      }

      // 如果有父分类，检查父分类是否存在
      if (data.parentId) {
        const parentCategory = await this.prisma.category.findUnique({
          where: { id: data.parentId },
        });

        if (!parentCategory) {
          throw new NotFoundError('父分类');
        }
      }

      const category = await this.prisma.category.create({
        data: {
          ...data,
          sortOrder: data.sortOrder || 0,
        },
        include: {
          parent: {
            select: {
              id: true,
              name: true,
              slug: true,
            },
          },
        },
      });

      logger.info('分类创建成功', {
        categoryId: category.id,
        name: category.name,
        userId: req.user?.id,
      });

      res.status(201).json(ApiResponse.created(category));
    } catch (error) {
      logger.error('创建分类失败', { error, data });
      throw error;
    }
  }

  /**
   * 更新分类（管理员）
   */
  public async updateCategory(req: Request, res: Response): Promise<void> {
    const { id } = req.params;
    const data: Partial<ICreateCategoryDto> = req.body;

    try {
      const categoryId = parseInt(id);
      
      // 检查分类是否存在
      const existingCategory = await this.prisma.category.findUnique({
        where: { id: categoryId },
      });

      if (!existingCategory) {
        throw new NotFoundError('分类');
      }

      // 如果更新slug，检查是否已存在
      if (data.slug && data.slug !== existingCategory.slug) {
        const slugExists = await this.prisma.category.findUnique({
          where: { slug: data.slug },
        });

        if (slugExists) {
          throw new ValidationError('分类标识符已存在');
        }
      }

      // 如果更新父分类，检查父分类是否存在且不是自己
      if (data.parentId) {
        if (data.parentId === categoryId) {
          throw new ValidationError('不能将自己设为父分类');
        }

        const parentCategory = await this.prisma.category.findUnique({
          where: { id: data.parentId },
        });

        if (!parentCategory) {
          throw new NotFoundError('父分类');
        }

        // 检查是否会形成循环引用
        const isCircular = await this.checkCircularReference(categoryId, data.parentId);
        if (isCircular) {
          throw new ValidationError('不能形成循环引用');
        }
      }

      const category = await this.prisma.category.update({
        where: { id: categoryId },
        data,
        include: {
          parent: {
            select: {
              id: true,
              name: true,
              slug: true,
            },
          },
        },
      });

      logger.info('分类更新成功', {
        categoryId: category.id,
        name: category.name,
        userId: req.user?.id,
      });

      res.json(ApiResponse.updated(category));
    } catch (error) {
      logger.error('更新分类失败', { error, id, data });
      throw error;
    }
  }

  /**
   * 删除分类（管理员）
   */
  public async deleteCategory(req: Request, res: Response): Promise<void> {
    const { id } = req.params;

    try {
      const categoryId = parseInt(id);
      
      const category = await this.prisma.category.findUnique({
        where: { id: categoryId },
        include: {
          children: true,
          products: true,
        },
      });

      if (!category) {
        throw new NotFoundError('分类');
      }

      // 检查是否有子分类
      if (category.children.length > 0) {
        throw new ValidationError('不能删除有子分类的分类');
      }

      // 检查是否有产品
      if (category.products.length > 0) {
        throw new ValidationError('不能删除有产品的分类');
      }

      await this.prisma.category.delete({
        where: { id: categoryId },
      });

      logger.info('分类删除成功', {
        categoryId,
        name: category.name,
        userId: req.user?.id,
      });

      res.json(ApiResponse.deleted());
    } catch (error) {
      logger.error('删除分类失败', { error, id });
      throw error;
    }
  }

  /**
   * 检查循环引用
   */
  private async checkCircularReference(categoryId: number, parentId: number): Promise<boolean> {
    let currentParentId = parentId;
    
    while (currentParentId) {
      if (currentParentId === categoryId) {
        return true;
      }
      
      const parent = await this.prisma.category.findUnique({
        where: { id: currentParentId },
        select: { parentId: true },
      });
      
      currentParentId = parent?.parentId || null;
    }
    
    return false;
  }
}
