/**
 * 联系咨询路由
 */

import { Router } from 'express';
import { body, query } from 'express-validator';
import { ContactController } from '../controllers/contact.controller';
import { authMiddleware, requireEditor } from '../middleware/auth';
import { asyncHandler } from '../middleware/errorHandler';
import { validateRequest } from '../middleware/validation';

const router = Router();
const contactController = new ContactController();

/**
 * 提交咨询验证规则
 */
const submitInquiryValidation = [
  body('name')
    .trim()
    .isLength({ min: 2, max: 100 })
    .withMessage('姓名长度必须在2-100个字符之间'),
  body('email')
    .isEmail()
    .normalizeEmail()
    .withMessage('请输入有效的邮箱地址'),
  body('phone')
    .optional()
    .isMobilePhone('zh-CN')
    .withMessage('请输入有效的手机号码'),
  body('company')
    .optional()
    .isLength({ max: 255 })
    .withMessage('公司名称不能超过255个字符'),
  body('subject')
    .trim()
    .isLength({ min: 2, max: 255 })
    .withMessage('主题长度必须在2-255个字符之间'),
  body('message')
    .trim()
    .isLength({ min: 10, max: 2000 })
    .withMessage('留言内容长度必须在10-2000个字符之间'),
];

/**
 * 回复咨询验证规则
 */
const replyInquiryValidation = [
  body('reply')
    .trim()
    .isLength({ min: 10, max: 2000 })
    .withMessage('回复内容长度必须在10-2000个字符之间'),
];

/**
 * 查询验证规则
 */
const queryValidation = [
  query('page')
    .optional()
    .isInt({ min: 1 })
    .withMessage('页码必须是正整数'),
  query('limit')
    .optional()
    .isInt({ min: 1, max: 100 })
    .withMessage('每页数量必须在1-100之间'),
  query('status')
    .optional()
    .isIn(['PENDING', 'IN_PROGRESS', 'RESOLVED', 'CLOSED'])
    .withMessage('状态值无效'),
];

/**
 * @swagger
 * /api/contact/submit:
 *   post:
 *     summary: 提交联系咨询
 *     tags: [联系咨询]
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required:
 *               - name
 *               - email
 *               - subject
 *               - message
 *             properties:
 *               name:
 *                 type: string
 *                 description: 姓名
 *               email:
 *                 type: string
 *                 format: email
 *                 description: 邮箱地址
 *               phone:
 *                 type: string
 *                 description: 手机号码
 *               company:
 *                 type: string
 *                 description: 公司名称
 *               subject:
 *                 type: string
 *                 description: 咨询主题
 *               message:
 *                 type: string
 *                 description: 留言内容
 *     responses:
 *       201:
 *         description: 提交成功
 *       400:
 *         description: 请求参数错误
 */
router.post('/submit',
  submitInquiryValidation,
  validateRequest,
  asyncHandler(contactController.submitInquiry.bind(contactController))
);

/**
 * @swagger
 * /api/contact/inquiries:
 *   get:
 *     summary: 获取咨询列表（管理员）
 *     tags: [联系咨询]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: query
 *         name: page
 *         schema:
 *           type: integer
 *           minimum: 1
 *         description: 页码
 *       - in: query
 *         name: limit
 *         schema:
 *           type: integer
 *           minimum: 1
 *           maximum: 100
 *         description: 每页数量
 *       - in: query
 *         name: status
 *         schema:
 *           type: string
 *           enum: [PENDING, IN_PROGRESS, RESOLVED, CLOSED]
 *         description: 咨询状态
 *       - in: query
 *         name: search
 *         schema:
 *           type: string
 *         description: 搜索关键词
 *     responses:
 *       200:
 *         description: 获取成功
 *       401:
 *         description: 未授权
 *       403:
 *         description: 权限不足
 */
router.get('/inquiries',
  authMiddleware,
  requireEditor,
  queryValidation,
  validateRequest,
  asyncHandler(contactController.getInquiries.bind(contactController))
);

/**
 * @swagger
 * /api/contact/inquiries/{id}:
 *   get:
 *     summary: 获取咨询详情（管理员）
 *     tags: [联系咨询]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: id
 *         required: true
 *         schema:
 *           type: integer
 *         description: 咨询ID
 *     responses:
 *       200:
 *         description: 获取成功
 *       401:
 *         description: 未授权
 *       403:
 *         description: 权限不足
 *       404:
 *         description: 咨询不存在
 */
router.get('/inquiries/:id',
  authMiddleware,
  requireEditor,
  asyncHandler(contactController.getInquiry.bind(contactController))
);

/**
 * @swagger
 * /api/contact/inquiries/{id}/reply:
 *   post:
 *     summary: 回复咨询（管理员）
 *     tags: [联系咨询]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: id
 *         required: true
 *         schema:
 *           type: integer
 *         description: 咨询ID
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required:
 *               - reply
 *             properties:
 *               reply:
 *                 type: string
 *                 description: 回复内容
 *     responses:
 *       200:
 *         description: 回复成功
 *       400:
 *         description: 请求参数错误
 *       401:
 *         description: 未授权
 *       403:
 *         description: 权限不足
 *       404:
 *         description: 咨询不存在
 */
router.post('/inquiries/:id/reply',
  authMiddleware,
  requireEditor,
  replyInquiryValidation,
  validateRequest,
  asyncHandler(contactController.replyInquiry.bind(contactController))
);

/**
 * @swagger
 * /api/contact/inquiries/{id}/status:
 *   put:
 *     summary: 更新咨询状态（管理员）
 *     tags: [联系咨询]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: id
 *         required: true
 *         schema:
 *           type: integer
 *         description: 咨询ID
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required:
 *               - status
 *             properties:
 *               status:
 *                 type: string
 *                 enum: [PENDING, IN_PROGRESS, RESOLVED, CLOSED]
 *                 description: 新状态
 *     responses:
 *       200:
 *         description: 更新成功
 *       400:
 *         description: 请求参数错误
 *       401:
 *         description: 未授权
 *       403:
 *         description: 权限不足
 *       404:
 *         description: 咨询不存在
 */
router.put('/inquiries/:id/status',
  authMiddleware,
  requireEditor,
  body('status')
    .isIn(['PENDING', 'IN_PROGRESS', 'RESOLVED', 'CLOSED'])
    .withMessage('状态值无效'),
  validateRequest,
  asyncHandler(contactController.updateInquiryStatus.bind(contactController))
);

export default router;
