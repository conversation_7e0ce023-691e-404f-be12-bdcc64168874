---
description: 
globs: 
alwaysApply: true
---
# 项目概述与技术栈规范

## 项目简介
这是一个企业级POS机产品展示网站，采用现代化技术栈开发，具备SEO优化、管理后台、一键安装等功能。

## 技术架构

### 前端（企业官网）
- **框架**: Nuxt.js 3 + Vue 3 + TypeScript
- **样式**: TailwindCSS 现代化样式框架
- **状态管理**: Pinia
- **SEO优化**: Nuxt SEO 模块
- **图片优化**: Nuxt Image 模块
- **构建工具**: Vite

### 后端（API服务）
- **运行时**: Node.js + Express + TypeScript
- **数据库**: MySQL 5.7 + Prisma ORM
- **认证**: JWT + bcrypt 加密
- **日志**: Winston 日志系统
- **文件上传**: Multer
- **API文档**: Swagger/OpenAPI

### 管理后台
- **框架**: Vue 3 + Element Plus + Vite
- **路由**: Vue Router 4
- **状态管理**: Pinia
- **HTTP客户端**: Axios
- **富文本编辑器**: TinyMCE

### 部署环境
- **服务器**: Rocky Linux 9.5
- **Web服务器**: Nginx (反向代理)
- **数据库**: MySQL 5.7
- **进程管理**: PM2
- **面板**: 宝塔LNMP环境
- **SSL**: 自动配置证书

## 项目目标
1. SEO友好的企业展示网站
2. 现代化UI/UX设计
3. 完整的管理后台系统
4. 一键安装部署向导
5. 高性能优化
6. 用户访问统计分析

## 开发环境
- **本地开发**: Windows 10/11
- **生产部署**: Rocky Linux 9.5 + 宝塔面板
- **版本控制**: Git
- **包管理器**: npm/yarn/pnpm

